#include <WiFi.h>
#include <WiFiClient.h>
#include <WebServer.h>
#include <esp_wifi.h>
#include <ESP32Servo.h>


// Communication Mode Configuration
enum CommunicationMode {
  MODE_USB_UART,
  MODE_WIFI
};

CommunicationMode currentMode = MODE_USB_UART; // Default to USB, fallback to WiFi
bool usbConnected = false;
unsigned long lastUsbCheck = 0;
const unsigned long USB_CHECK_INTERVAL = 1000; // Check USB every 1 second

// Ultrasonic Sensor Configuration - Smart Greeting Integration
const int ULTRASONIC_TRIG_PIN = 12;
const int ULTRASONIC_ECHO_PIN = 13;
const float SMART_GREETING_DISTANCE = 30.0; // 30cm threshold for handshake range
const float HUMAN_DETECTION_DISTANCE = 50.0; // Legacy 50cm threshold (for compatibility)
bool humanDetectionEnabled = false; // Disabled by default - Smart Greeting handles detection
bool humanDetected = false;
unsigned long lastHumanDetectionCheck = 0;
const unsigned long HUMAN_DETECTION_INTERVAL = 200; // Check every 200ms
unsigned long lastHumanDetectedTime = 0;
const unsigned long HUMAN_GREETING_COOLDOWN = 15000; // 15 seconds between greetings (matches Android app)

// Real-time distance streaming for Smart Greeting
bool distanceStreamingEnabled = false;
unsigned long lastDistanceStream = 0;
const unsigned long DISTANCE_STREAM_INTERVAL = 200; // Stream every 200ms for real-time updates (optimized for Android)

// WiFi Configuration for Station Mode
const char* station_ssid = "Ziyad"; // Replace with your WiFi network name
const char* station_password = "1234567890"; // Replace with your WiFi password

// WiFi Configuration for Access Point Mode
const char* ap_ssid = "ROBO SOCCER"; // Access Point SSID
const char* ap_password = "123456789"; // Access Point password

// WiFi Control - Disable WiFi when USB is connected for better performance

// Mecanum Wheel Motor Control Pins (4 Motors for Omnidirectional Movement)
// Front Left Motor (Motor A)
const int MOTOR_FL_PIN1 = 25;
const int MOTOR_FL_PIN2 = 26;
// Front Right Motor (Motor B)
const int MOTOR_FR_PIN1 = 14;
const int MOTOR_FR_PIN2 = 15;
// Rear Left Motor (Motor C)
const int MOTOR_RL_PIN1 = 32;
const int MOTOR_RL_PIN2 = 33;
// Rear Right Motor (Motor D)
const int MOTOR_RR_PIN1 = 27;
const int MOTOR_RR_PIN2 = 4;

// Legacy pin definitions for backward compatibility
const int MOTOR_A_PIN1 = MOTOR_FL_PIN1;
const int MOTOR_A_PIN2 = MOTOR_FL_PIN2;
const int MOTOR_B_PIN1 = MOTOR_FR_PIN1;
const int MOTOR_B_PIN2 = MOTOR_FR_PIN2;

// Status LED
const int STATUS_LED = 2;

// Robot Configuration
enum RobotType {
  MECANUM_WHEEL,
  NORMAL_TWO_WHEEL
};

// Default robot type (can be changed via command)
RobotType currentRobotType = MECANUM_WHEEL;

// Servo Control Pins for Arms
const int SERVO_LEFT_ARM = 18;   // Left arm servo pin
const int SERVO_RIGHT_ARM = 19;   // Right arm servo pin

// Servo Control Pins for Head
const int SERVO_HEAD_PAN = 23;   // Head pan servo pin (left/right rotation)
const int SERVO_HEAD_TILT = 5;  // Head tilt servo pin (up/down rotation)

// Servo objects
Servo leftArmServo;
Servo rightArmServo;
Servo headPanServo;
Servo headTiltServo;

// Servo positions
int leftArmPosition = 180;   // Initial position (rest for left arm - 180 degrees)
int rightArmPosition = 0;    // Initial position (rest for right arm - 0 degrees)
int headPanPosition = 90;   // Initial position (center)
int headTiltPosition = 90;  // Initial position (center)

const int LEFT_ARM_REST_POSITION = 180;  // Left arm rest position
const int RIGHT_ARM_REST_POSITION = 0;   // Right arm rest position
const int LEFT_ARM_WAVE_POSITION = 135;  // Left arm wave position
const int RIGHT_ARM_WAVE_POSITION = 45;  // Right arm wave position
const int LEFT_ARM_POINT_POSITION = 225; // Left arm point position
const int RIGHT_ARM_POINT_POSITION = 315; // Right arm point position
const int LEFT_ARM_GREETING_POSITION = 140; // Left arm greeting position
const int RIGHT_ARM_GREETING_POSITION = 40;  // Right arm greeting position

// Head servo positions
const int HEAD_CENTER_POSITION = 90;
const int HEAD_LOOK_LEFT_POSITION = 45;   // Look left (pan servo)
const int HEAD_LOOK_RIGHT_POSITION = 135; // Look right (pan servo)
const int HEAD_LOOK_UP_POSITION = 45;     // Look up (tilt servo)
const int HEAD_LOOK_DOWN_POSITION = 135;  // Look down (tilt servo)

// Smooth servo movement variables (configurable speed control)
int currentLeftArmAngle = 180;   // Track current left arm position (rest at 180°)
int currentRightArmAngle = 0;    // Track current right arm position (rest at 0°)
int currentHeadPanAngle = 90;   // Track current head pan position
int currentHeadTiltAngle = 90;  // Track current head tilt position

// Configurable movement speed (delay between steps in milliseconds)
int servoMoveDelay = 15;        // Default 15ms delay for smooth movement
const int MIN_SERVO_DELAY = 5;  // Minimum delay (fastest movement)
const int MAX_SERVO_DELAY = 50; // Maximum delay (slowest movement)

// Configurable handshake duration (can be set from Android app)
unsigned long handshakeDuration = 5000; // Default 5 seconds (configurable)

// Human-like walking animation variables
bool isWalking = false;
unsigned long lastWalkUpdate = 0;
unsigned long walkAnimationInterval = 800; // Default 800ms per arm cycle (configurable)
int walkAnimationStep = 0; // 0=left moving, 1=left returning, 2=right moving, 3=right returning
int walkSwingAngle = 20; // Default 20 degrees swing (configurable)

// Configurable rest positions for each hand
int leftArmRestPosition = 0;  // Default 0° (configurable from Android)
int rightArmRestPosition = 0; // Default 0° (configurable from Android)

// Command override and gesture control variables
bool gestureInProgress = false;
bool commandOverrideRequested = false;
unsigned long gestureStartTime = 0;
String currentGesture = "";
int gestureStep = 0;
unsigned long lastGestureUpdate = 0;

WebServer server(80);
bool wifiStationConnected = false;

// Dedicated function to start WiFi Access Point immediately
void startWiFiAccessPoint() {
  Serial.println("🚀 STARTING WIFI ACCESS POINT - PRIORITY 1");

  // Force WiFi to stop any existing connections
  WiFi.disconnect(true);
  WiFi.mode(WIFI_OFF);
  delay(500);

  // Set to Access Point mode only initially
  WiFi.mode(WIFI_AP);
  delay(200);

  // Start Access Point with explicit configuration
  Serial.printf("Starting Access Point: %s\n", ap_ssid);
  bool success = WiFi.softAP(ap_ssid, ap_password, 1, 0, 4);

  if (success) {
    Serial.println("✅ WIFI ACCESS POINT STARTED SUCCESSFULLY!");
    Serial.printf("✅ SSID: %s\n", ap_ssid);
    Serial.printf("✅ Password: %s\n", ap_password);
    Serial.printf("✅ IP Address: %s\n", WiFi.softAPIP().toString().c_str());
    Serial.printf("✅ Connect your smartphone to '%s' and open http://%s\n", ap_ssid, WiFi.softAPIP().toString().c_str());
  } else {
    Serial.println("❌ FAILED TO START ACCESS POINT - RETRYING...");
    delay(1000);
    WiFi.mode(WIFI_AP);
    delay(200);
    success = WiFi.softAP(ap_ssid, ap_password);
    if (success) {
      Serial.println("✅ ACCESS POINT STARTED ON RETRY!");
      Serial.printf("✅ IP Address: %s\n", WiFi.softAPIP().toString().c_str());
    } else {
      Serial.println("❌ ACCESS POINT FAILED COMPLETELY!");
    }
  }

  Serial.println("🚀 WIFI ACCESS POINT INITIALIZATION COMPLETE");
}

void setup() {
  // Initialize USB Serial communication FIRST (highest priority)
  Serial.begin(115200);
  Serial.setTimeout(100); // Quick timeout for responsive serial reading

  Serial.println("\n=== STEM-Xpert ROBO Controller v5.0 ===");
  Serial.println("PRIORITY: WiFi Access Point > USB Serial > WiFi Station");
  Serial.println("USB Serial Communication: 115200 baud, 8N1");

  // IMMEDIATELY START WIFI ACCESS POINT (HIGHEST PRIORITY)
  startWiFiAccessPoint();

  // Initialize mecanum wheel motor pins
  pinMode(MOTOR_FL_PIN1, OUTPUT);
  pinMode(MOTOR_FL_PIN2, OUTPUT);
  pinMode(MOTOR_FR_PIN1, OUTPUT);
  pinMode(MOTOR_FR_PIN2, OUTPUT);
  pinMode(MOTOR_RL_PIN1, OUTPUT);
  pinMode(MOTOR_RL_PIN2, OUTPUT);
  pinMode(MOTOR_RR_PIN1, OUTPUT);
  pinMode(MOTOR_RR_PIN2, OUTPUT);
  pinMode(STATUS_LED, OUTPUT);

  // Initialize ultrasonic sensor pins
  pinMode(ULTRASONIC_TRIG_PIN, OUTPUT);
  pinMode(ULTRASONIC_ECHO_PIN, INPUT);

  // Initialize servo pins and attach servos
  leftArmServo.attach(SERVO_LEFT_ARM);
  rightArmServo.attach(SERVO_RIGHT_ARM);
  headPanServo.attach(SERVO_HEAD_PAN);
  headTiltServo.attach(SERVO_HEAD_TILT);

  // Set initial servo positions (0-180° range, reflection handled in Android app)
  leftArmServo.write(LEFT_ARM_REST_POSITION);   // Left arm: 180° (direct)
  rightArmServo.write(RIGHT_ARM_REST_POSITION); // Right arm: 0° (direct)
  headPanServo.write(HEAD_CENTER_POSITION);
  headTiltServo.write(HEAD_CENTER_POSITION);

  // Store servo positions
  leftArmPosition = LEFT_ARM_REST_POSITION;   // 180°
  rightArmPosition = RIGHT_ARM_REST_POSITION; // 0°
  headPanPosition = HEAD_CENTER_POSITION;
  headTiltPosition = HEAD_CENTER_POSITION;

  // Update current angle tracking
  currentLeftArmAngle = LEFT_ARM_REST_POSITION;   // 180°
  currentRightArmAngle = RIGHT_ARM_REST_POSITION; // 0°

  // Initialize smooth movement tracking for all servos
  currentLeftArmAngle = LEFT_ARM_REST_POSITION;  // 180 degrees for left arm
  currentRightArmAngle = RIGHT_ARM_REST_POSITION; // 0 degrees for right arm
  currentHeadPanAngle = HEAD_CENTER_POSITION;
  currentHeadTiltAngle = HEAD_CENTER_POSITION;

  // Start with motors stopped
  stopRobot();

  // WiFi Access Point already started - just verify it's working
  Serial.println("Verifying WiFi Access Point status...");
  if (WiFi.getMode() == WIFI_AP || WiFi.getMode() == WIFI_AP_STA) {
    Serial.println("✅ WiFi Access Point is active and ready");
  } else {
    Serial.println("⚠️ WiFi Access Point not detected - restarting...");
    startWiFiAccessPoint();
  }

  // Setup comprehensive web server routes for smartphone control
  server.on("/", HTTP_handleRoot);
  server.on("/status", HTTP_handleStatus);
  server.on("/heartbeat", HTTP_handleHeartbeat);
  server.onNotFound(HTTP_handleRoot);

  // Start web server for smartphone control
  server.begin();
  Serial.println("WiFi Access Point server started on port 80");
  Serial.println("Smartphone control available at: http://" + WiFi.softAPIP().toString());

  // Check USB connection for dual control setup
  checkUSBConnection();

  if (usbConnected) {
    Serial.println("USB Serial detected - DUAL CONTROL MODE: USB (Primary) + WiFi AP (Secondary)");
    currentMode = MODE_USB_UART;

    // Auto-enable distance streaming for Smart Greeting when USB connected
    distanceStreamingEnabled = true;
    Serial.println("USB_STATUS: Distance Streaming Auto-Enabled for Smart Greeting");
  } else {
    Serial.println("No USB connection - WiFi Access Point control active");
    currentMode = MODE_WIFI;
  }

  Serial.println("=== STEM-Xpert ROBO Ready ===");
  Serial.println("COMMUNICATION MODE: " + String(currentMode == MODE_USB_UART ? "DUAL CONTROL (USB + WiFi AP)" : "WiFi Access Point"));
  Serial.println("ULTRASONIC SENSOR: Pins 12(Trig), 13(Echo) - ACTIVE");
  Serial.println("DISTANCE STREAMING: " + String(distanceStreamingEnabled ? "ENABLED" : "DISABLED"));
  Serial.println("");

  // Verify WiFi Access Point status
  if (WiFi.softAPgetStationNum() >= 0) {
    Serial.println("✅ WiFi Access Point is ACTIVE and ready for connections");
    Serial.printf("📱 SSID: %s\n", ap_ssid);
    Serial.printf("🔑 Password: %s\n", ap_password);
    Serial.printf("🌐 Web Control: http://%s\n", WiFi.softAPIP().toString().c_str());
  } else {
    Serial.println("❌ WiFi Access Point failed to start!");
  }
  Serial.println("");

  Serial.println("USB Serial Commands: F, B, L, R, S, SL, SR, DFL, DFR, DBL, DBR, ROT_L, ROT_R");
  Serial.println("Mecanum Commands: SL(Slide Left), SR(Slide Right), DFL(Diagonal Front-Left)");
  Serial.println("                  DFR(Diagonal Front-Right), DBL(Diagonal Back-Left), DBR(Diagonal Back-Right)");
  Serial.println("                  ROT_L(Rotate Left), ROT_R(Rotate Right)");
  Serial.println("Servo Commands: WAVE, POINT, REST, LA[0-360], RA[0-360]");
  Serial.println("Head Commands: HP[0-180], HT[0-180], LOOK_LEFT, LOOK_RIGHT, CENTER_HEAD");
  Serial.println("Ultrasonic Commands: GET_DISTANCE, STREAM_DISTANCE_ON, STREAM_DISTANCE_OFF");
  Serial.println("Smart Greeting Commands: HANDSHAKE, HELLO");
  Serial.println("Smart Greeting: Face Detection + Distance ≤30cm → Handshake + Voice");
  Serial.println("Send commands as single line (e.g., 'GET_DISTANCE' + Enter)");
  Serial.println("Ready for commands...");
  printConnectionStatus();
}

void loop() {
  // PRIORITY 1: USB Serial Communication (HIGHEST PRIORITY)
  // Check for USB Serial commands first and most frequently
  if (Serial.available()) {
    handleUSBSerialCommunication();
  }

  // Periodically check USB connection status
  if (millis() - lastUsbCheck >= USB_CHECK_INTERVAL) {
    checkUSBConnection();
    lastUsbCheck = millis();
  }

  // PRIORITY 2: WiFi Communication (ALWAYS ACTIVE FOR SMARTPHONE CONTROL)
  // Handle WiFi Access Point requests for smartphone control
  server.handleClient();

  // Monitor WiFi connection status
  if (WiFi.status() != WL_CONNECTED && wifiStationConnected) {
    Serial.println("WiFi station disconnected - Access Point still active");
    wifiStationConnected = false;
  } else if (WiFi.status() == WL_CONNECTED && !wifiStationConnected) {
    Serial.println("WiFi station reconnected - Dual WiFi mode active");
    wifiStationConnected = true;
  }

  // Update status LED (WiFi AP always active, USB optional)
  bool hasConnection = Serial || wifiStationConnected;
  digitalWrite(STATUS_LED, hasConnection ? HIGH : LOW);

  // Handle command override - stop all ongoing operations
  if (commandOverrideRequested) {
    stopAllOperations();
    commandOverrideRequested = false;
  }

  // Handle walking animation
  if (isWalking && (millis() - lastWalkUpdate >= walkAnimationInterval)) {
    updateWalkingAnimation();
    lastWalkUpdate = millis();
  }

  // Handle non-blocking gesture execution
  if (gestureInProgress) {
    updateGestureExecution();
  }

  // Handle human detection with ultrasonic sensor (legacy mode)
  if (humanDetectionEnabled && (millis() - lastHumanDetectionCheck >= HUMAN_DETECTION_INTERVAL)) {
    checkHumanDetection();
    lastHumanDetectionCheck = millis();
  }

  // Handle real-time distance streaming for Smart Greeting
  if (distanceStreamingEnabled && (millis() - lastDistanceStream >= DISTANCE_STREAM_INTERVAL)) {
    streamDistanceReading();
    lastDistanceStream = millis();
  }

  // Monitor WiFi Access Point status (every 30 seconds)
  static unsigned long lastWiFiCheck = 0;
  if (millis() - lastWiFiCheck >= 30000) {
    if (WiFi.getMode() != WIFI_AP_STA && WiFi.getMode() != WIFI_AP) {
      Serial.println("⚠️ WiFi Access Point lost - Restarting...");
      startWiFiAccessPoint();
    }
    lastWiFiCheck = millis();
  }

  // Minimal delay for USB Serial responsiveness
  delay(5);
}


void HTTP_handleRoot() {
  // Check for command parameter and execute if present
  if (server.hasArg("cmd")) {
    String command = server.arg("cmd");
    processCommand(command);
    server.send(200, "text/plain", "Command: " + command + " executed");
    Serial.println("WiFi Command received: " + command);
    return;
  }

  // Serve comprehensive web control interface
  String html = R"rawliteral(
<!DOCTYPE html>
<html>
<head>
    <title>STEM Robot Control</title>
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <style>
        body {
            font-family: Arial, sans-serif;
            text-align: center;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            margin: 0;
            padding: 20px;
            -webkit-user-select: none; /* Safari */
            -ms-user-select: none; /* IE 10+ */
            user-select: none; /* Standard syntax */
        }
        .container {
            max-width: 600px;
            margin: 0 auto;
            background: rgba(255,255,255,0.1);
            border-radius: 15px;
            padding: 20px;
            backdrop-filter: blur(10px);
        }
        h1 {
            color: #fff;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
            margin-bottom: 30px;
        }
        .section {
            margin: 20px 0;
            padding: 15px;
            background: rgba(255,255,255,0.1);
            border-radius: 10px;
        }
        .section h2 {
            margin-top: 0;
            color: #ffeb3b;
        }
        button {
            width: 100px;
            height: 50px;
            font-size: 14px;
            margin: 6px;
            background: linear-gradient(45deg, #4CAF50, #45a049);
            color: white;
            border: none;
            border-radius: 10px;
            cursor: pointer;
            transition: all 0.3s;
            box-shadow: 0 4px 8px rgba(0,0,0,0.2);
        }
        button:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 12px rgba(0,0,0,0.3);
        }
        button:active {
            transform: translateY(0);
            box-shadow: 0 2px 4px rgba(0,0,0,0.2);
        }
        .movement-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 10px;
            max-width: 340px;
            margin: 0 auto;
        }
        .gesture-btn { background: linear-gradient(45deg, #FF6B6B, #ee5a52); }
        .movement-btn { background: linear-gradient(45deg, #4ECDC4, #44a08d); }
        .head-btn { background: linear-gradient(45deg, #45B7D1, #3a9bc1); }
        .stop-btn { background: linear-gradient(45deg, #FFA726, #ff9800); }
        .status {
            margin-top: 20px;
            padding: 10px;
            background: rgba(0,0,0,0.2);
            border-radius: 5px;
            font-family: monospace;
        }
        .omni-controls { display: none; }
        /* Toggle Switch Styles */
        .switch {
            position: relative;
            display: inline-block;
            width: 60px;
            height: 34px;
        }
        .switch input {
            opacity: 0;
            width: 0;
            height: 0;
        }
        .slider {
            position: absolute;
            cursor: pointer;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: #ccc;
            transition: .4s;
            border-radius: 34px;
        }
        .slider:before {
            position: absolute;
            content: "";
            height: 26px;
            width: 26px;
            left: 4px;
            bottom: 4px;
            background-color: white;
            transition: .4s;
            border-radius: 50%;
        }
        input:checked + .slider {
            background-color: #2196F3;
        }
        input:checked + .slider:before {
            transform: translateX(26px);
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🤖 STEM Robot Control</h1>

        <div class="section">
            <h2>Drive Mode</h2>
            <span>Normal</span>
            <label class="switch">
                <input type="checkbox" id="omniToggle" onchange="toggleOmni()">
                <span class="slider"></span>
            </label>
            <span>Omni (Mecanum)</span>
        </div>

        <div class="section">
            <h2 id="movement-title">🎮 Normal Movement</h2>
            <div class="movement-grid">
                <button class="movement-btn omni-controls" data-cmd="DFL">↖</button>
                <button class="movement-btn" data-cmd="F">↑</button>
                <button class="movement-btn omni-controls" data-cmd="DFR">↗</button>
                <button class="movement-btn" data-cmd="L">←</button>
                <button class="stop-btn" onclick="send('S')">STOP</button>
                <button class="movement-btn" data-cmd="R">→</button>
                <button class="movement-btn omni-controls" data-cmd="DBL">↙</button>
                <button class="movement-btn" data-cmd="B">↓</button>
                <button class="movement-btn omni-controls" data-cmd="DBR">↘</button>
            </div>
        </div>

        <div class="section omni-controls">
            <h2>🔄 Advanced Movement</h2>
            <button class="movement-btn" data-cmd="SL">Slide ←</button>
            <button class="movement-btn" data-cmd="SR">Slide →</button>
            <button class="movement-btn" data-cmd="ROT_L">↺ Rotate Left</button>
            <button class="movement-btn" data-cmd="ROT_R">↻ Rotate Right</button>
        </div>

        <div class="section">
            <h2>🤝 Gestures</h2>
            <button class="gesture-btn" onclick="send('WAVE')">👋 Wave</button>
            <button class="gesture-btn" onclick="send('POINT')">👉 Point</button>
            <button class="gesture-btn" onclick="send('HANDSHAKE')">🤝 Handshake</button>
            <button class="gesture-btn" onclick="send('REST')">🏠 Rest</button>
        </div>

        <div class="section">
            <h2>🗣️ Head Movement</h2>
            <button class="head-btn" onclick="send('LOOK_LEFT')">← Look Left</button>
            <button class="head-btn" onclick="send('CENTER_HEAD')">⚫ Center</button>
            <button class="head-btn" onclick="send('LOOK_RIGHT')">→ Look Right</button>
        </div>

        <div class="status" id="status">
            Ready for commands...
        </div>
    </div>

    <script>
        function send(cmd) {
            document.getElementById('status').innerHTML = 'Sending: ' + cmd + '...';
            fetch('/?cmd=' + cmd)
                .then(response => response.text())
                .then(data => {
                    document.getElementById('status').innerHTML = data;
                })
                .catch(error => {
                    document.getElementById('status').innerHTML = 'Error: ' + error;
                });
        }

        function toggleOmni() {
            const isOmni = document.getElementById('omniToggle').checked;
            const omniControls = document.querySelectorAll('.omni-controls');
            const movementTitle = document.getElementById('movement-title');
            
            if (isOmni) {
                send('SET_MECANUM');
                movementTitle.innerText = '🎮 Omni Movement';
                omniControls.forEach(el => el.style.display = 'block');
                // Remap left/right buttons for mecanum rotation
                document.querySelector('button[data-cmd="L"]').setAttribute('data-cmd', 'ROT_L');
                document.querySelector('button[data-cmd="R"]').setAttribute('data-cmd', 'ROT_R');

            } else {
                send('SET_NORMAL');
                movementTitle.innerText = '🎮 Normal Movement';
                omniControls.forEach(el => el.style.display = 'none');
                 // Remap left/right buttons for normal turning
                document.querySelector('button[data-cmd="ROT_L"]').setAttribute('data-cmd', 'L');
                document.querySelector('button[data-cmd="ROT_R"]').setAttribute('data-cmd', 'R');
            }
        }

        document.querySelectorAll('.movement-btn').forEach(button => {
            const cmd = button.getAttribute('data-cmd');
            if (cmd) {
                button.addEventListener('mousedown', () => send(cmd));
                button.addEventListener('mouseup', () => send('S'));
                button.addEventListener('mouseleave', () => send('S'));
                button.addEventListener('touchstart', (e) => { e.preventDefault(); send(cmd); });
                button.addEventListener('touchend', () => send('S'));
            }
        });
        
        // Initial setup
        toggleOmni();

        // Update status every 5 seconds
        setInterval(function() {
            fetch('/status')
                .then(response => response.json())
                .then(data => {
                    if (data.status === 'online') {
                        document.getElementById('status').innerHTML =
                            '🟢 Online | Mode: ' + data.primary_mode.toUpperCase() +
                            ' | Distance: ' + data.ultrasonic_distance.toFixed(1) + 'cm';
                    }
                })
                .catch(error => {
                    // Ignore status update errors
                });
        }, 5000);
    </script>
</body>
</html>
)rawliteral";

  server.send(200, "text/html", html);
}

void HTTP_handleStatus() {
  // Create JSON response with robot status
  String response = "{";
  response += "\"status\":\"online\",";
  response += "\"controller\":\"motor\",";
  response += "\"primary_mode\":\"" + String(currentMode == MODE_USB_UART ? "usb" : "wifi") + "\",";
  response += "\"usb_connected\":" + String(usbConnected ? "true" : "false") + ",";
  response += "\"wifi_station_connected\":" + String(wifiStationConnected ? "true" : "false") + ",";
  response += "\"wifi_ap_active\":true,";
  response += "\"wifi_strength\":" + String(WiFi.RSSI()) + ",";
  response += "\"uptime\":" + String(millis()) + ",";
  response += "\"free_heap\":" + String(ESP.getFreeHeap()) + ",";
  response += "\"station_ip\":\"" + WiFi.localIP().toString() + "\",";
  response += "\"ap_ip\":\"" + WiFi.softAPIP().toString() + "\",";
  response += "\"communication_modes\":[";
  if (usbConnected) response += "\"usb\",";
  if (wifiStationConnected) response += "\"wifi_station\",";
  response += "\"wifi_ap\"],";
  response += "\"human_detection_enabled\":" + String(humanDetectionEnabled ? "true" : "false") + ",";
  response += "\"human_detected\":" + String(humanDetected ? "true" : "false") + ",";
  response += "\"ultrasonic_distance\":" + String(getUltrasonicDistance()) + ",";
  response += "\"version\":\"5.1.0\"";
  response += "}";

  server.send(200, "application/json", response);
  Serial.println("Status request handled via WiFi - All modes active: USB=" + String(usbConnected) +
                 ", WiFi_Station=" + String(wifiStationConnected) + ", WiFi_AP=true");
}

void HTTP_handleHeartbeat() {
  // Simple heartbeat endpoint for connection monitoring
  server.send(200, "text/plain", "OK");
}

// USB Serial Communication Handler (PRIORITY COMMUNICATION)
void handleUSBSerialCommunication() {
  String command = Serial.readStringUntil('\n');
  command.trim();

  if (command.length() > 0) {
    Serial.println("USB_RX: " + command); // Echo received command

    // Process command immediately (USB Serial has highest priority)
    processCommand(command);

    // Send acknowledgment back via USB Serial
    Serial.println("USB_ACK: " + command + "_OK");

    // Update connection status
    usbConnected = true;
    currentMode = MODE_USB_UART;
  }
}

void processCommand(String cmd) {
  // Convert command to uppercase for consistency
  cmd.toUpperCase();

  // IMMEDIATE COMMAND OVERRIDE - Stop all ongoing operations first
  commandOverrideRequested = true;

  Serial.println("USB_CMD: " + cmd + " - Processing...");

  // Robot configuration commands
  if (cmd == "SET_MECANUM") {
    currentRobotType = MECANUM_WHEEL;
    Serial.println("USB_STATUS: Robot Type Set to Mecanum Wheel - CONFIGURED");
  }
  else if (cmd == "SET_NORMAL") {
    currentRobotType = NORMAL_TWO_WHEEL;
    Serial.println("USB_STATUS: Robot Type Set to Normal Two-Wheel - CONFIGURED");
  }
  // Motor commands with USB Serial feedback
  else if (cmd == "F") {
    goForward();
    Serial.println("USB_STATUS: Moving Forward - ACTIVE");
  }
  else if (cmd == "B") {
    goBack();
    Serial.println("USB_STATUS: Moving Backward - ACTIVE");
  }
  else if (cmd == "L") {
    goLeft();
    Serial.println("USB_STATUS: Turning Left - ACTIVE");
  }
  else if (cmd == "R") {
    goRight();
    Serial.println("USB_STATUS: Turning Right - ACTIVE");
  }
  else if (cmd == "S") {
    stopRobot();
    Serial.println("USB_STATUS: Robot Stopped - IDLE");
  }
  // Mecanum wheel omnidirectional movement commands
  else if (cmd == "SL") {
    slideLeft();
    Serial.println("USB_STATUS: Sliding Left - ACTIVE");
  }
  else if (cmd == "SR") {
    slideRight();
    Serial.println("USB_STATUS: Sliding Right - ACTIVE");
  }
  else if (cmd == "DFL") {
    diagonalFrontLeft();
    Serial.println("USB_STATUS: Diagonal Front-Left - ACTIVE");
  }
  else if (cmd == "DFR") {
    diagonalFrontRight();
    Serial.println("USB_STATUS: Diagonal Front-Right - ACTIVE");
  }
  else if (cmd == "DBL") {
    diagonalBackLeft();
    Serial.println("USB_STATUS: Diagonal Back-Left - ACTIVE");
  }
  else if (cmd == "DBR") {
    diagonalBackRight();
    Serial.println("USB_STATUS: Diagonal Back-Right - ACTIVE");
  }
  else if (cmd == "ROT_L") {
    rotateLeft();
    Serial.println("USB_STATUS: Rotating Left - ACTIVE");
  }
  else if (cmd == "ROT_R") {
    rotateRight();
    Serial.println("USB_STATUS: Rotating Right - ACTIVE");
  }
  // Servo commands with USB Serial feedback
  else if (cmd == "WAVE") {
    startNonBlockingWave();
    Serial.println("USB_STATUS: Wave Gesture - EXECUTING");
  }
  else if (cmd == "POINT") {
    startNonBlockingPoint();
    Serial.println("USB_STATUS: Point Gesture - EXECUTING");
  }
  else if (cmd == "REST") {
    performRest();
    Serial.println("USB_STATUS: Rest Position - COMPLETE");
  }
  else if (cmd.startsWith("LA")) { // Left Arm - format: LA90
    int angle = cmd.substring(2).toInt();
    setLeftArmWithOverride(angle);
    Serial.println("USB_STATUS: Left Arm " + String(angle) + "° - COMPLETE");
  }
  else if (cmd.startsWith("RA")) { // Right Arm - format: RA90
    int angle = cmd.substring(2).toInt();
    setRightArmWithOverride(angle);
    Serial.println("USB_STATUS: Right Arm " + String(angle) + "° - COMPLETE");
  }
  else if (cmd.startsWith("HP")) { // Head Pan - format: HP90
    int angle = cmd.substring(2).toInt();
    setHeadPanWithOverride(angle);
    Serial.println("USB_STATUS: Head Pan " + String(angle) + "° - COMPLETE");
  }
  else if (cmd.startsWith("HT")) { // Head Tilt - format: HT90
    int angle = cmd.substring(2).toInt();
    setHeadTiltWithOverride(angle);
    Serial.println("USB_STATUS: Head Tilt " + String(angle) + "° - COMPLETE");
  }
  else if (cmd == "STOP_ALL") { // Emergency stop all operations
    stopAllOperations();
    stopRobot();
    Serial.println("USB_STATUS: Emergency Stop - ALL HALTED");
  }
  else if (cmd == "STATUS") { // USB Serial status request
    sendUSBStatus();
  }
  else if (cmd == "HELLO") { // Manual hello greeting
    performHelloGreeting();
    Serial.println("USB_STATUS: Hello Greeting - EXECUTING");
  }
  else if (cmd == "HUMAN_DETECT_ON") { // Enable human detection
    humanDetectionEnabled = true;
    Serial.println("USB_STATUS: Human Detection Enabled - ACTIVE");
  }
  else if (cmd == "HUMAN_DETECT_OFF") { // Disable human detection
    humanDetectionEnabled = false;
    Serial.println("USB_STATUS: Human Detection Disabled - INACTIVE");
  }
  else if (cmd == "GET_DISTANCE") { // Get current ultrasonic distance
    float distance = getUltrasonicDistance();
    Serial.println("USB_DISTANCE: " + String(distance) + " cm");
  }
  else if (cmd == "HANDSHAKE") { // Smart Greeting handshake gesture
    performHandshakeGreeting();
    Serial.println("USB_STATUS: Handshake Greeting - EXECUTING");
  }
  else if (cmd.startsWith("SET_HANDSHAKE_DURATION:")) { // Set handshake duration
    String durationStr = cmd.substring(23); // Get duration after "SET_HANDSHAKE_DURATION:"
    unsigned long duration = durationStr.toInt();
    if (duration >= 1000 && duration <= 30000) { // 1-30 seconds range
      handshakeDuration = duration;
      Serial.println("USB_STATUS: Handshake duration set to " + String(duration) + "ms");
    } else {
      Serial.println("USB_ERROR: Invalid duration. Use 1000-30000ms (1-30 seconds)");
    }
  }
  else if (cmd == "STREAM_DISTANCE_ON") { // Enable real-time distance streaming
    distanceStreamingEnabled = true;
    Serial.println("USB_STATUS: Distance Streaming Enabled - ACTIVE");
  }
  else if (cmd == "STREAM_DISTANCE_OFF") { // Disable real-time distance streaming
    distanceStreamingEnabled = false;
    Serial.println("USB_STATUS: Distance Streaming Disabled - INACTIVE");
  }
  // Simple Smart Greeting Commands for Android App Control
  else if (cmd == "GREETING_START") { // Move arm to greeting position smoothly
    moveRightArmSmoothly(RIGHT_ARM_GREETING_POSITION); // Move to 40 degrees
    Serial.println("USB_STATUS: Greeting gesture started - arm at 40°");
  }
  else if (cmd == "GREETING_END") { // Return arm to rest position smoothly
    moveRightArmSmoothly(RIGHT_ARM_REST_POSITION); // Move to 0 degrees
    Serial.println("USB_STATUS: Greeting gesture ended - arm at rest (0°)");
  }
  else if (cmd.startsWith("SMOOTH_MOVE:")) { // Move arm smoothly to specific angle
    int angle = cmd.substring(12).toInt();
    if (angle >= 0 && angle <= 360) {
      moveRightArmSmoothly(angle);
      Serial.println("USB_STATUS: Smooth move to " + String(angle) + "° completed");
    } else {
      Serial.println("USB_ERROR: Invalid angle. Use 0-360 degrees");
    }
  }
  // Head movement predefined actions
  else if (cmd == "LOOK_LEFT") {
    setHeadPanWithOverride(HEAD_LOOK_LEFT_POSITION);
    Serial.println("USB_STATUS: Head Look Left - COMPLETE");
  }
  else if (cmd == "LOOK_RIGHT") {
    setHeadPanWithOverride(HEAD_LOOK_RIGHT_POSITION);
    Serial.println("USB_STATUS: Head Look Right - COMPLETE");
  }
  else if (cmd == "CENTER_HEAD") {
    setHeadPanWithOverride(HEAD_CENTER_POSITION);
    setHeadTiltWithOverride(HEAD_CENTER_POSITION);
    Serial.println("USB_STATUS: Head Centered - COMPLETE");
  }
  // Servo speed control
  else if (cmd.startsWith("SET_SERVO_SPEED:")) { // Set servo movement speed
    int speed = cmd.substring(16).toInt();
    if (speed >= MIN_SERVO_DELAY && speed <= MAX_SERVO_DELAY) {
      servoMoveDelay = speed;
      Serial.println("USB_STATUS: Servo speed set to " + String(speed) + "ms delay");
    } else {
      Serial.println("USB_ERROR: Invalid speed. Use " + String(MIN_SERVO_DELAY) + "-" + String(MAX_SERVO_DELAY) + "ms");
    }
  }
  else if (cmd.startsWith("SET_WALK_TIMING:")) { // Set walking animation timing
    String timingStr = cmd.substring(16); // Get timing after "SET_WALK_TIMING:"
    unsigned long timing = timingStr.toInt();
    if (timing >= 200 && timing <= 3000) { // 200ms to 3 seconds per step
      walkAnimationInterval = timing;
      Serial.println("USB_STATUS: Walk timing set to " + String(timing) + "ms per step");
    } else {
      Serial.println("USB_ERROR: Invalid timing. Use 200-3000ms");
    }
  }
  else if (cmd.startsWith("SET_LEFT_REST:")) { // Set left arm rest position
    String angleStr = cmd.substring(14); // Get angle after "SET_LEFT_REST:"
    int angle = angleStr.toInt();
    if (angle >= 0 && angle <= 180) {
      leftArmRestPosition = angle;
      Serial.println("USB_STATUS: Left arm rest position set to " + String(angle) + "°");
      // If not walking, move to new rest position immediately
      if (!isWalking && !gestureInProgress) {
        setLeftArm(leftArmRestPosition);
      }
    } else {
      Serial.println("USB_ERROR: Invalid angle. Use 0-180°");
    }
  }
  else if (cmd.startsWith("SET_RIGHT_REST:")) { // Set right arm rest position
    String angleStr = cmd.substring(15); // Get angle after "SET_RIGHT_REST:"
    int angle = angleStr.toInt();
    if (angle >= 0 && angle <= 180) {
      rightArmRestPosition = angle;
      Serial.println("USB_STATUS: Right arm rest position set to " + String(angle) + "°");
      // If not walking, move to new rest position immediately
      if (!isWalking && !gestureInProgress) {
        setRightArm(rightArmRestPosition);
      }
    } else {
      Serial.println("USB_ERROR: Invalid angle. Use 0-180°");
    }
  }
  else {
    Serial.println("USB_ERROR: Unknown command: " + cmd);
    Serial.println("USB_HELP: Basic movement: F,B,L,R,S");
    Serial.println("USB_HELP: Mecanum movement: SL,SR,DFL,DFR,DBL,DBR,ROT_L,ROT_R");
    Serial.println("USB_HELP: Servo commands: WAVE,POINT,REST,LA[0-360],RA[0-360],HELLO,HANDSHAKE");
    Serial.println("USB_HELP: Head commands: HP[0-180],HT[0-180],LOOK_LEFT,LOOK_RIGHT,CENTER_HEAD");
    Serial.println("USB_HELP: Speed control: SET_SERVO_SPEED:[5-50ms] (lower=faster, higher=slower)");
    Serial.println("USB_HELP: Sensor commands: GET_DISTANCE,STREAM_DISTANCE_ON,STREAM_DISTANCE_OFF");
    Serial.println("USB_HELP: Smart Greeting: GREETING_START,GREETING_END,SMOOTH_MOVE:[0-360]");
    Serial.println("USB_HELP: Handshake config: SET_HANDSHAKE_DURATION:[1000-30000ms]");
    Serial.println("USB_HELP: Walking config: SET_WALK_TIMING:[200-3000ms], SET_LEFT_REST:[0-180°], SET_RIGHT_REST:[0-180°]");
  }
}

// Motor Control Functions (Digital Control Only) - with immediate override
// Updated to support both mecanum wheel and normal two-wheel configurations
void goForward() {
  // Stop any ongoing servo gestures immediately
  if (gestureInProgress) {
    stopAllOperations();
  }

  if (currentRobotType == MECANUM_WHEEL) {
    // Mecanum: All motors forward
    digitalWrite(MOTOR_FL_PIN1, HIGH); digitalWrite(MOTOR_FL_PIN2, LOW);  // FL forward
    digitalWrite(MOTOR_FR_PIN1, HIGH); digitalWrite(MOTOR_FR_PIN2, LOW);  // FR forward
    digitalWrite(MOTOR_RL_PIN1, HIGH); digitalWrite(MOTOR_RL_PIN2, LOW);  // RL forward
    digitalWrite(MOTOR_RR_PIN1, HIGH); digitalWrite(MOTOR_RR_PIN2, LOW);  // RR forward
  } else {
    // Normal two-wheel: Only front motors (left and right wheels)
    digitalWrite(MOTOR_FL_PIN1, HIGH); digitalWrite(MOTOR_FL_PIN2, LOW);  // Left wheel forward
    digitalWrite(MOTOR_FR_PIN1, HIGH); digitalWrite(MOTOR_FR_PIN2, LOW);  // Right wheel forward
    // Rear motors off for normal robot
    digitalWrite(MOTOR_RL_PIN1, LOW); digitalWrite(MOTOR_RL_PIN2, LOW);
    digitalWrite(MOTOR_RR_PIN1, LOW); digitalWrite(MOTOR_RR_PIN2, LOW);
  }

  // Start walking animation
  startWalkingAnimation();
}

void goBack() {
  // Stop any ongoing servo gestures immediately
  if (gestureInProgress) {
    stopAllOperations();
  }

  if (currentRobotType == MECANUM_WHEEL) {
    // Mecanum: All motors backward
    digitalWrite(MOTOR_FL_PIN1, LOW); digitalWrite(MOTOR_FL_PIN2, HIGH);  // FL backward
    digitalWrite(MOTOR_FR_PIN1, LOW); digitalWrite(MOTOR_FR_PIN2, HIGH);  // FR backward
    digitalWrite(MOTOR_RL_PIN1, LOW); digitalWrite(MOTOR_RL_PIN2, HIGH);  // RL backward
    digitalWrite(MOTOR_RR_PIN1, LOW); digitalWrite(MOTOR_RR_PIN2, HIGH);  // RR backward
  } else {
    // Normal two-wheel: Only front motors (left and right wheels)
    digitalWrite(MOTOR_FL_PIN1, LOW); digitalWrite(MOTOR_FL_PIN2, HIGH);  // Left wheel backward
    digitalWrite(MOTOR_FR_PIN1, LOW); digitalWrite(MOTOR_FR_PIN2, HIGH);  // Right wheel backward
    // Rear motors off for normal robot
    digitalWrite(MOTOR_RL_PIN1, LOW); digitalWrite(MOTOR_RL_PIN2, LOW);
    digitalWrite(MOTOR_RR_PIN1, LOW); digitalWrite(MOTOR_RR_PIN2, LOW);
  }

  // Start walking animation
  startWalkingAnimation();
}

void goRight() {
  // Stop any ongoing servo gestures immediately
  if (gestureInProgress) {
    stopAllOperations();
  }

  if (currentRobotType == MECANUM_WHEEL) {
    // Mecanum: Rotate Right (Clockwise): FL+RL forward, FR+RR backward
    digitalWrite(MOTOR_FL_PIN1, HIGH); digitalWrite(MOTOR_FL_PIN2, LOW);  // FL forward
    digitalWrite(MOTOR_FR_PIN1, LOW); digitalWrite(MOTOR_FR_PIN2, HIGH);  // FR backward
    digitalWrite(MOTOR_RL_PIN1, HIGH); digitalWrite(MOTOR_RL_PIN2, LOW);  // RL forward
    digitalWrite(MOTOR_RR_PIN1, LOW); digitalWrite(MOTOR_RR_PIN2, HIGH);  // RR backward
  } else {
    // Normal two-wheel: Turn right (left wheel forward, right wheel backward)
    digitalWrite(MOTOR_FL_PIN1, HIGH); digitalWrite(MOTOR_FL_PIN2, LOW);  // Left wheel forward
    digitalWrite(MOTOR_FR_PIN1, LOW); digitalWrite(MOTOR_FR_PIN2, HIGH);  // Right wheel backward
    // Rear motors off for normal robot
    digitalWrite(MOTOR_RL_PIN1, LOW); digitalWrite(MOTOR_RL_PIN2, LOW);
    digitalWrite(MOTOR_RR_PIN1, LOW); digitalWrite(MOTOR_RR_PIN2, LOW);
  }

  // Start walking animation
  startWalkingAnimation();
}

void goLeft() {
  // Stop any ongoing servo gestures immediately
  if (gestureInProgress) {
    stopAllOperations();
  }

  if (currentRobotType == MECANUM_WHEEL) {
    // Mecanum: Rotate Left (Counter-Clockwise): FL+RL backward, FR+RR forward
    digitalWrite(MOTOR_FL_PIN1, LOW); digitalWrite(MOTOR_FL_PIN2, HIGH);  // FL backward
    digitalWrite(MOTOR_FR_PIN1, HIGH); digitalWrite(MOTOR_FR_PIN2, LOW);  // FR forward
    digitalWrite(MOTOR_RL_PIN1, LOW); digitalWrite(MOTOR_RL_PIN2, HIGH);  // RL backward
    digitalWrite(MOTOR_RR_PIN1, HIGH); digitalWrite(MOTOR_RR_PIN2, LOW);  // RR forward
  } else {
    // Normal two-wheel: Turn left (left wheel backward, right wheel forward)
    digitalWrite(MOTOR_FL_PIN1, LOW); digitalWrite(MOTOR_FL_PIN2, HIGH);  // Left wheel backward
    digitalWrite(MOTOR_FR_PIN1, HIGH); digitalWrite(MOTOR_FR_PIN2, LOW);  // Right wheel forward
    // Rear motors off for normal robot
    digitalWrite(MOTOR_RL_PIN1, LOW); digitalWrite(MOTOR_RL_PIN2, LOW);
    digitalWrite(MOTOR_RR_PIN1, LOW); digitalWrite(MOTOR_RR_PIN2, LOW);
  }

  // Start walking animation
  startWalkingAnimation();
}

void stopRobot() {
  // Stop all operations immediately
  stopAllOperations();

  // Stop all mecanum wheel motors
  digitalWrite(MOTOR_FL_PIN1, LOW);
  digitalWrite(MOTOR_FL_PIN2, LOW);
  digitalWrite(MOTOR_FR_PIN1, LOW);
  digitalWrite(MOTOR_FR_PIN2, LOW);
  digitalWrite(MOTOR_RL_PIN1, LOW);
  digitalWrite(MOTOR_RL_PIN2, LOW);
  digitalWrite(MOTOR_RR_PIN1, LOW);
  digitalWrite(MOTOR_RR_PIN2, LOW);

  // Stop walking animation and return to rest position
  stopWalkingAnimation();
}

// Mecanum Wheel Omnidirectional Movement Functions
// Motor Layout: FL(Front-Left), FR(Front-Right), RL(Rear-Left), RR(Rear-Right)
// Mecanum wheel movement patterns for omnidirectional control

void slideLeft() {
  // Only available for mecanum wheel robots
  if (currentRobotType != MECANUM_WHEEL) {
    Serial.println("USB_ERROR: Slide commands only available for mecanum wheel robots");
    return;
  }

  // Stop any ongoing servo gestures immediately
  if (gestureInProgress) {
    stopAllOperations();
  }

  // Slide Left: FL(backward), FR(forward), RL(forward), RR(backward) - CORRECTED
  digitalWrite(MOTOR_FL_PIN1, LOW);   digitalWrite(MOTOR_FL_PIN2, HIGH);  // FL backward
  digitalWrite(MOTOR_FR_PIN1, HIGH);  digitalWrite(MOTOR_FR_PIN2, LOW);   // FR forward
  digitalWrite(MOTOR_RL_PIN1, HIGH);  digitalWrite(MOTOR_RL_PIN2, LOW);   // RL forward
  digitalWrite(MOTOR_RR_PIN1, LOW);   digitalWrite(MOTOR_RR_PIN2, HIGH);  // RR backward

  // Start walking animation for visual feedback
  startWalkingAnimation();
}

void slideRight() {
  // Only available for mecanum wheel robots
  if (currentRobotType != MECANUM_WHEEL) {
    Serial.println("USB_ERROR: Slide commands only available for mecanum wheel robots");
    return;
  }

  // Stop any ongoing servo gestures immediately
  if (gestureInProgress) {
    stopAllOperations();
  }

  // Slide Right: FL(forward), FR(backward), RL(backward), RR(forward) - CORRECTED
  digitalWrite(MOTOR_FL_PIN1, HIGH);  digitalWrite(MOTOR_FL_PIN2, LOW);   // FL forward
  digitalWrite(MOTOR_FR_PIN1, LOW);   digitalWrite(MOTOR_FR_PIN2, HIGH);  // FR backward
  digitalWrite(MOTOR_RL_PIN1, LOW);   digitalWrite(MOTOR_RL_PIN2, HIGH);  // RL backward
  digitalWrite(MOTOR_RR_PIN1, HIGH);  digitalWrite(MOTOR_RR_PIN2, LOW);   // RR forward

  // Start walking animation for visual feedback
  startWalkingAnimation();
}

void diagonalFrontLeft() {
  // Only available for mecanum wheel robots
  if (currentRobotType != MECANUM_WHEEL) {
    Serial.println("USB_ERROR: Diagonal commands only available for mecanum wheel robots");
    return;
  }

  // Stop any ongoing servo gestures immediately
  if (gestureInProgress) {
    stopAllOperations();
  }

  // Diagonal Front-Left: FR(forward), RL(forward), FL+RR(stop) - CORRECTED
  digitalWrite(MOTOR_FL_PIN1, LOW);   digitalWrite(MOTOR_FL_PIN2, LOW);   // FL stop
  digitalWrite(MOTOR_FR_PIN1, HIGH);  digitalWrite(MOTOR_FR_PIN2, LOW);   // FR forward
  digitalWrite(MOTOR_RL_PIN1, HIGH);  digitalWrite(MOTOR_RL_PIN2, LOW);   // RL forward
  digitalWrite(MOTOR_RR_PIN1, LOW);   digitalWrite(MOTOR_RR_PIN2, LOW);   // RR stop

  // Start walking animation for visual feedback
  startWalkingAnimation();
}

void diagonalFrontRight() {
  // Stop any ongoing servo gestures immediately
  if (gestureInProgress) {
    stopAllOperations();
  }

  // Diagonal Front-Right: FL(forward), RR(forward), FR+RL(stop) - CORRECTED
  digitalWrite(MOTOR_FL_PIN1, HIGH);  digitalWrite(MOTOR_FL_PIN2, LOW);   // FL forward
  digitalWrite(MOTOR_FR_PIN1, LOW);   digitalWrite(MOTOR_FR_PIN2, LOW);   // FR stop
  digitalWrite(MOTOR_RL_PIN1, LOW);   digitalWrite(MOTOR_RL_PIN2, LOW);   // RL stop
  digitalWrite(MOTOR_RR_PIN1, HIGH);  digitalWrite(MOTOR_RR_PIN2, LOW);   // RR forward

  // Start walking animation for visual feedback
  startWalkingAnimation();
}

void diagonalBackLeft() {
  // Stop any ongoing servo gestures immediately
  if (gestureInProgress) {
    stopAllOperations();
  }

  // Diagonal Back-Left: FL(backward), RR(backward), FR+RL(stop) - CORRECTED
  digitalWrite(MOTOR_FL_PIN1, LOW);   digitalWrite(MOTOR_FL_PIN2, HIGH);  // FL backward
  digitalWrite(MOTOR_FR_PIN1, LOW);   digitalWrite(MOTOR_FR_PIN2, LOW);   // FR stop
  digitalWrite(MOTOR_RL_PIN1, LOW);   digitalWrite(MOTOR_RL_PIN2, LOW);   // RL stop
  digitalWrite(MOTOR_RR_PIN1, LOW);   digitalWrite(MOTOR_RR_PIN2, HIGH);  // RR backward

  // Start walking animation for visual feedback
  startWalkingAnimation();
}

void diagonalBackRight() {
  // Stop any ongoing servo gestures immediately
  if (gestureInProgress) {
    stopAllOperations();
  }

  // Diagonal Back-Right: FR(backward), RL(backward), FL+RR(stop) - CORRECTED
  digitalWrite(MOTOR_FL_PIN1, LOW);   digitalWrite(MOTOR_FL_PIN2, LOW);   // FL stop
  digitalWrite(MOTOR_FR_PIN1, LOW);   digitalWrite(MOTOR_FR_PIN2, HIGH);  // FR backward
  digitalWrite(MOTOR_RL_PIN1, LOW);   digitalWrite(MOTOR_RL_PIN2, HIGH);  // RL backward
  digitalWrite(MOTOR_RR_PIN1, LOW);   digitalWrite(MOTOR_RR_PIN2, LOW);   // RR stop

  // Start walking animation for visual feedback
  startWalkingAnimation();
}

void rotateLeft() {
  // Only available for mecanum wheel robots
  if (currentRobotType != MECANUM_WHEEL) {
    Serial.println("USB_ERROR: Rotate commands only available for mecanum wheel robots");
    return;
  }

  // Stop any ongoing servo gestures immediately
  if (gestureInProgress) {
    stopAllOperations();
  }

  // Rotate Left (Counter-Clockwise): FL+RL(backward), FR+RR(forward) - CORRECTED
  digitalWrite(MOTOR_FL_PIN1, LOW);   digitalWrite(MOTOR_FL_PIN2, HIGH);  // FL backward
  digitalWrite(MOTOR_FR_PIN1, HIGH);  digitalWrite(MOTOR_FR_PIN2, LOW);   // FR forward
  digitalWrite(MOTOR_RL_PIN1, LOW);   digitalWrite(MOTOR_RL_PIN2, HIGH);  // RL backward
  digitalWrite(MOTOR_RR_PIN1, HIGH);  digitalWrite(MOTOR_RR_PIN2, LOW);   // RR forward

  // Start walking animation for visual feedback
  startWalkingAnimation();
}

void rotateRight() {
  // Stop any ongoing servo gestures immediately
  if (gestureInProgress) {
    stopAllOperations();
  }

  // Rotate Right (Clockwise): FL+RL(forward), FR+RR(backward) - CORRECTED
  digitalWrite(MOTOR_FL_PIN1, HIGH);  digitalWrite(MOTOR_FL_PIN2, LOW);   // FL forward
  digitalWrite(MOTOR_FR_PIN1, LOW);   digitalWrite(MOTOR_FR_PIN2, HIGH);  // FR backward
  digitalWrite(MOTOR_RL_PIN1, HIGH);  digitalWrite(MOTOR_RL_PIN2, LOW);   // RL forward
  digitalWrite(MOTOR_RR_PIN1, LOW);   digitalWrite(MOTOR_RR_PIN2, HIGH);  // RR backward

  // Start walking animation for visual feedback
  startWalkingAnimation();
}

// Smooth Servo Movement Functions
void moveLeftArmSmoothly(int targetAngle) {
  targetAngle = constrain(targetAngle, 0, 180); // 0-180 degree range for standard servos

  // Left arm: Direct mapping (working perfectly as user confirmed)
  Serial.println("SERVO_SMOOTH: Moving left arm from " + String(currentLeftArmAngle) + "° to " + String(targetAngle) + "°");

  if (targetAngle > currentLeftArmAngle) {
    for (int i = currentLeftArmAngle; i <= targetAngle; i++) {
      leftArmServo.write(i);
      delay(servoMoveDelay);
    }
  } else {
    for (int i = currentLeftArmAngle; i >= targetAngle; i--) {
      leftArmServo.write(i);
      delay(servoMoveDelay);
    }
  }

  currentLeftArmAngle = targetAngle;
  leftArmPosition = targetAngle;
  Serial.println("SERVO_SMOOTH: Left arm moved to " + String(targetAngle) + "° - COMPLETED");
}

void moveRightArmSmoothly(int targetAngle) {
  targetAngle = constrain(targetAngle, 0, 180); // 0-180 degree range for standard servos

  // Right arm: Direct mapping (reflection is handled in Android app)
  Serial.println("SERVO_SMOOTH: Moving right arm from " + String(currentRightArmAngle) + "° to " + String(targetAngle) + "°");

  if (targetAngle > currentRightArmAngle) {
    for (int i = currentRightArmAngle; i <= targetAngle; i++) {
      rightArmServo.write(i);
      delay(servoMoveDelay);
    }
  } else {
    for (int i = currentRightArmAngle; i >= targetAngle; i--) {
      rightArmServo.write(i);
      delay(servoMoveDelay);
    }
  }

  currentRightArmAngle = targetAngle;
  rightArmPosition = targetAngle;
  Serial.println("SERVO_SMOOTH: Right arm moved to " + String(targetAngle) + "° - COMPLETED");
}

// Legacy functions updated to use smooth movement
void setLeftArm(int angle) {
  moveLeftArmSmoothly(angle);
}

void setRightArm(int angle) {
  moveRightArmSmoothly(angle);
}

// Individual servo control with immediate override
void setLeftArmWithOverride(int angle) {
  stopAllOperations(); // Stop any ongoing operations
  setLeftArm(angle);
  Serial.println("Left arm set to " + String(angle) + " degrees (with override)");
}

void setRightArmWithOverride(int angle) {
  stopAllOperations(); // Stop any ongoing operations
  setRightArm(angle);
  Serial.println("Right arm set to " + String(angle) + " degrees (with override)");
}

// Smooth Head Movement Functions
void moveHeadPanSmoothly(int targetAngle) {
  targetAngle = constrain(targetAngle, 0, 180);

  Serial.println("SERVO_SMOOTH: Moving head pan from " + String(currentHeadPanAngle) + "° to " + String(targetAngle) + "°");

  if (targetAngle > currentHeadPanAngle) {
    for (int i = currentHeadPanAngle; i <= targetAngle; i++) {
      headPanServo.write(i);
      delay(servoMoveDelay);
    }
  } else {
    for (int i = currentHeadPanAngle; i >= targetAngle; i--) {
      headPanServo.write(i);
      delay(servoMoveDelay);
    }
  }

  currentHeadPanAngle = targetAngle;
  headPanPosition = targetAngle;
  Serial.println("SERVO_SMOOTH: Head pan moved to " + String(targetAngle) + "° - COMPLETED");
}

void moveHeadTiltSmoothly(int targetAngle) {
  targetAngle = constrain(targetAngle, 0, 180);

  Serial.println("SERVO_SMOOTH: Moving head tilt from " + String(currentHeadTiltAngle) + "° to " + String(targetAngle) + "°");

  if (targetAngle > currentHeadTiltAngle) {
    for (int i = currentHeadTiltAngle; i <= targetAngle; i++) {
      headTiltServo.write(i);
      delay(servoMoveDelay);
    }
  } else {
    for (int i = currentHeadTiltAngle; i >= targetAngle; i--) {
      headTiltServo.write(i);
      delay(servoMoveDelay);
    }
  }

  currentHeadTiltAngle = targetAngle;
  headTiltPosition = targetAngle;
  Serial.println("SERVO_SMOOTH: Head tilt moved to " + String(targetAngle) + "° - COMPLETED");
}

// Legacy functions updated to use smooth movement
void setHeadPan(int angle) {
  moveHeadPanSmoothly(angle);
}

void setHeadTilt(int angle) {
  moveHeadTiltSmoothly(angle);
}

// Individual head servo control with immediate override
void setHeadPanWithOverride(int angle) {
  stopAllOperations(); // Stop any ongoing operations
  setHeadPan(angle);
  Serial.println("Head pan set to " + String(angle) + " degrees (with override)");
}

void setHeadTiltWithOverride(int angle) {
  stopAllOperations(); // Stop any ongoing operations
  setHeadTilt(angle);
  Serial.println("Head tilt set to " + String(angle) + " degrees (with override)");
}



// Non-blocking Gesture Functions
void startNonBlockingWave() {
  stopAllOperations(); // Stop any ongoing operations
  gestureInProgress = true;
  currentGesture = "WAVE";
  gestureStep = 0;
  gestureStartTime = millis();
  lastGestureUpdate = millis();
  Serial.println("Starting non-blocking wave gesture");
}

void startNonBlockingPoint() {
  stopAllOperations(); // Stop any ongoing operations
  gestureInProgress = true;
  currentGesture = "POINT";
  gestureStep = 0;
  gestureStartTime = millis();
  lastGestureUpdate = millis();
  Serial.println("Starting non-blocking point gesture");
}

void performRest() {
  // Immediate rest position - this is always immediate override
  stopAllOperations();
  setLeftArm(LEFT_ARM_REST_POSITION);
  setRightArm(RIGHT_ARM_REST_POSITION);
  Serial.println("Arms moved to rest position - Left: 180°, Right: 0°");
}

// Blocking versions kept for compatibility but with override check
void performWave() {
  if (gestureInProgress) return; // Don't start if gesture in progress
  startNonBlockingWave();
}

void performPoint() {
  if (gestureInProgress) return; // Don't start if gesture in progress
  startNonBlockingPoint();
}

// Walking Animation Functions
void startWalkingAnimation() {
  isWalking = true;
  walkAnimationStep = 0; // Reset to first step
  lastWalkUpdate = millis();
}

void stopWalkingAnimation() {
  isWalking = false;
  walkAnimationStep = 0; // Reset animation step

  // Return arms to their configured rest positions
  Serial.println("WALK_STOP: Returning arms to configured rest positions");
  setLeftArm(leftArmRestPosition);
  setRightArm(rightArmRestPosition);
}

void updateWalkingAnimation() {
  if (!isWalking) return;

  unsigned long currentTime = millis();
  if (currentTime - lastWalkUpdate < walkAnimationInterval) return;

  lastWalkUpdate = currentTime;

  // Human-like alternating arm movement: Left moves while right rests, then vice versa
  switch (walkAnimationStep) {
    case 0: // Left arm moves from rest to swing position (0° to 20°)
      Serial.println("WALK_STEP_0: Left arm moving to " + String(leftArmRestPosition + walkSwingAngle) + "°");
      setLeftArm(leftArmRestPosition + walkSwingAngle);
      setRightArm(rightArmRestPosition); // Right arm stays at rest
      walkAnimationStep = 1;
      break;

    case 1: // Left arm returns to rest position (20° to 0°)
      Serial.println("WALK_STEP_1: Left arm returning to rest " + String(leftArmRestPosition) + "°");
      setLeftArm(leftArmRestPosition);
      walkAnimationStep = 2;
      break;

    case 2: // Right arm moves from rest to swing position (0° to 20°)
      Serial.println("WALK_STEP_2: Right arm moving to " + String(rightArmRestPosition + walkSwingAngle) + "°");
      setRightArm(rightArmRestPosition + walkSwingAngle);
      setLeftArm(leftArmRestPosition); // Left arm stays at rest
      walkAnimationStep = 3;
      break;

    case 3: // Right arm returns to rest position (20° to 0°)
      Serial.println("WALK_STEP_3: Right arm returning to rest " + String(rightArmRestPosition) + "°");
      setRightArm(rightArmRestPosition);
      walkAnimationStep = 0; // Reset cycle
      break;
  }
}

// Command Override and Gesture Management Functions
void stopAllOperations() {
  // Stop walking animation
  isWalking = false;

  // Stop any ongoing gesture
  gestureInProgress = false;
  currentGesture = "";
  gestureStep = 0;

  Serial.println("All operations stopped - ready for new command");
}

void updateGestureExecution() {
  if (!gestureInProgress) return;

  unsigned long currentTime = millis();

  if (currentGesture == "WAVE") {
    executeWaveGesture(currentTime);
  } else if (currentGesture == "POINT") {
    executePointGesture(currentTime);
  } else if (currentGesture == "HELLO") {
    executeHelloGesture(currentTime);
  } else if (currentGesture == "HANDSHAKE") {
    executeHandshakeGesture(currentTime);
  }
}

void executeWaveGesture(unsigned long currentTime) {
  switch (gestureStep) {
    case 0: // Start - move to wave position
      setRightArm(RIGHT_ARM_WAVE_POSITION);
      gestureStep = 1;
      lastGestureUpdate = currentTime;
      break;

    case 1: // Wait 500ms, then move to rest
      if (currentTime - lastGestureUpdate >= 500) {
        setRightArm(RIGHT_ARM_REST_POSITION);
        gestureStep = 2;
        lastGestureUpdate = currentTime;
      }
      break;

    case 2: // Wait 300ms, then wave again
      if (currentTime - lastGestureUpdate >= 300) {
        setRightArm(RIGHT_ARM_WAVE_POSITION);
        gestureStep = 3;
        lastGestureUpdate = currentTime;
      }
      break;

    case 3: // Wait 500ms, then return to rest and finish
      if (currentTime - lastGestureUpdate >= 500) {
        setRightArm(RIGHT_ARM_REST_POSITION);
        gestureInProgress = false;
        currentGesture = "";
        gestureStep = 0;
        Serial.println("Wave gesture completed - Right arm returned to rest (0°)");
      }
      break;
  }
}

void executePointGesture(unsigned long currentTime) {
  switch (gestureStep) {
    case 0: // Start - move to point position
      setRightArm(RIGHT_ARM_POINT_POSITION);
      gestureStep = 1;
      lastGestureUpdate = currentTime;
      break;

    case 1: // Hold for 2000ms, then return to rest
      if (currentTime - lastGestureUpdate >= 2000) {
        setRightArm(RIGHT_ARM_REST_POSITION);
        gestureInProgress = false;
        currentGesture = "";
        gestureStep = 0;
        Serial.println("Point gesture completed - Right arm returned to rest (0°)");
      }
      break;
  }
}

// USB Communication Functions
void checkUSBConnection() {
  // Enhanced USB connection detection with better status reporting
  bool previousUsbState = usbConnected;

  #if ARDUINO_USB_CDC_ON_BOOT
    // For ESP32-S3 and other CDC-enabled boards
    usbConnected = Serial && (Serial.availableForWrite() > 0);
  #else
    // For standard ESP32 boards, check if Serial is initialized and responsive
    usbConnected = Serial && Serial.availableForWrite() >= 0;
  #endif

  // Update communication mode based on USB status
  if (usbConnected) {
    currentMode = MODE_USB_UART;
    // Auto-enable distance streaming for accurate readings
    if (!distanceStreamingEnabled) {
      distanceStreamingEnabled = true;
      Serial.println("USB_SENSOR: Distance streaming auto-enabled");
    }
  } else {
    currentMode = MODE_WIFI;
  }

  // Log state changes with detailed information
  if (previousUsbState != usbConnected) {
    if (usbConnected) {
      Serial.println("USB_CONNECT: USB Serial connection established");
      Serial.println("USB_MODE: Switched to USB Serial communication (PRIMARY)");
      Serial.println("USB_READY: Ready for USB commands at 115200 baud");
      Serial.println("USB_ULTRASONIC: Pins 12(Trig), 13(Echo) - Real-time streaming active");
    } else {
      // This message may not be seen if USB is truly disconnected
      Serial.println("USB_DISCONNECT: USB Serial lost - WiFi Access Point remains active");
    }
    // Print full status after connection change
    printConnectionStatus();
  }
}

void handleUSBCommunication() {
  if (Serial.available()) {
    String command = Serial.readStringUntil('\n');
    command.trim();

    if (command.length() > 0) {
      Serial.println("USB Command received: " + command);

      // Handle status request via USB
      if (command.equalsIgnoreCase("STATUS")) {
        sendUSBStatus();
      } else {
        // Process regular robot commands
        processCommand(command);
        Serial.println("USB Command executed: " + command);
      }
    }
  }
}

void sendUSBStatus() {
  Serial.println("USB_STATUS_START");
  Serial.println("{");
  Serial.println("  \"status\": \"online\",");
  Serial.println("  \"controller\": \"motor_servo\",");
  Serial.println("  \"communication_mode\": \"dual_control_usb_wifi\",");
  Serial.println("  \"usb_connected\": true,");
  Serial.println("  \"baud_rate\": 115200,");
  Serial.printf("  \"wifi_strength\": %d,\n", WiFi.RSSI());
  Serial.printf("  \"uptime\": %lu,\n", millis());
  Serial.printf("  \"free_heap\": %d,\n", ESP.getFreeHeap());
  Serial.printf("  \"station_connected\": %s,\n", wifiStationConnected ? "true" : "false");
  Serial.printf("  \"station_ip\": \"%s\",\n", WiFi.localIP().toString().c_str());
  Serial.printf("  \"ap_ip\": \"%s\",\n", WiFi.softAPIP().toString().c_str());
  Serial.printf("  \"left_arm_position\": %d,\n", leftArmPosition);
  Serial.printf("  \"right_arm_position\": %d,\n", rightArmPosition);
  Serial.printf("  \"head_pan_position\": %d,\n", headPanPosition);
  Serial.printf("  \"head_tilt_position\": %d,\n", headTiltPosition);
  Serial.printf("  \"walking_active\": %s,\n", isWalking ? "true" : "false");
  Serial.printf("  \"gesture_active\": %s,\n", gestureInProgress ? "true" : "false");
  Serial.printf("  \"human_detection_enabled\": %s,\n", humanDetectionEnabled ? "true" : "false");
  Serial.printf("  \"human_detected\": %s,\n", humanDetected ? "true" : "false");
  Serial.printf("  \"ultrasonic_distance\": %.2f,\n", getUltrasonicDistance());
  Serial.println("  \"version\": \"5.1.0\"");
  Serial.println("}");
  Serial.println("USB_STATUS_END");
}

// Connection Status Functions
void printConnectionStatus() {
  Serial.println("=== DUAL CONTROL COMMUNICATION STATUS ===");
  Serial.printf("Control Mode: %s\n", currentMode == MODE_USB_UART ? "DUAL (USB + WiFi AP)" : "WiFi Access Point Only");
  Serial.println("--- Active Communication Channels ---");
  Serial.printf("1. USB UART: %s\n", usbConnected ? "ACTIVE (Primary)" : "INACTIVE");
  Serial.printf("2. WiFi Station: %s\n", wifiStationConnected ? "CONNECTED" : "DISCONNECTED");
  Serial.println("3. WiFi Access Point: ALWAYS ACTIVE (Smartphone Control)");
  Serial.println("--- Network Information ---");
  if (wifiStationConnected) {
    Serial.printf("Station IP: %s\n", WiFi.localIP().toString().c_str());
  } else {
    Serial.println("Station IP: Not connected");
  }
  Serial.printf("Access Point IP: %s (SSID: ROBO SOCCER)\n", WiFi.softAPIP().toString().c_str());
  Serial.printf("Web Interface: http://%s\n", WiFi.softAPIP().toString().c_str());
  Serial.printf("Free Heap: %d bytes\n", ESP.getFreeHeap());
  Serial.println("--- Control Capabilities ---");
  Serial.println("• Tablet Control: " + String(usbConnected ? "USB Serial" : "WiFi"));
  Serial.println("• Smartphone Control: WiFi Access Point (Always Available)");
  Serial.println("--- Command Reception ---");
  Serial.println("Commands accepted via:");
  if (usbConnected) Serial.println("  - USB Serial (direct)");
  if (wifiStationConnected) Serial.println("  - WiFi Station HTTP");
  Serial.println("  - WiFi AP HTTP (always)");
  Serial.println("===================================");
}

// Diagnostic Functions
void printStatus() {
  printConnectionStatus();
}

// Ultrasonic Sensor Functions - Enhanced for accuracy
float getUltrasonicDistance() {
  // Ensure clean signal
  digitalWrite(ULTRASONIC_TRIG_PIN, LOW);
  delayMicroseconds(5); // Longer stabilization time

  // Send trigger pulse
  digitalWrite(ULTRASONIC_TRIG_PIN, HIGH);
  delayMicroseconds(10);
  digitalWrite(ULTRASONIC_TRIG_PIN, LOW);

  // Read echo pulse duration with longer timeout for better range
  long duration = pulseIn(ULTRASONIC_ECHO_PIN, HIGH, 35000); // 35ms timeout for ~6m range

  // Calculate distance in cm (speed of sound = 343 m/s at 20°C)
  // Formula: distance = (duration * speed_of_sound) / 2
  // Speed of sound = 343 m/s = 0.0343 cm/µs
  float distance = (duration * 0.0343) / 2.0;

  // Validate reading
  if (duration == 0 || distance < 2.0 || distance > 400.0) {
    return 999.0; // Out of range or invalid reading
  }

  // Round to 1 decimal place for consistency
  return round(distance * 10.0) / 10.0;
}

void checkHumanDetection() {
  if (!humanDetectionEnabled) return;

  float distance = getUltrasonicDistance();
  bool currentHumanDetected = (distance <= HUMAN_DETECTION_DISTANCE && distance > 0);

  // Human detected for the first time or after cooldown period
  if (currentHumanDetected && !humanDetected) {
    unsigned long currentTime = millis();

    // Check if enough time has passed since last greeting
    if (currentTime - lastHumanDetectedTime >= HUMAN_GREETING_COOLDOWN) {
      humanDetected = true;
      lastHumanDetectedTime = currentTime;

      Serial.println("HUMAN_DETECTED: Distance " + String(distance) + "cm - Greeting initiated");
      performHelloGreeting();
    }
  }
  // Human no longer detected
  else if (!currentHumanDetected && humanDetected) {
    humanDetected = false;
    Serial.println("HUMAN_LOST: Distance " + String(distance) + "cm - Human moved away");
  }
}

void performHelloGreeting() {
  // Stop any ongoing operations for greeting
  stopAllOperations();

  // Start non-blocking hello gesture (right arm wave + "hello" message)
  gestureInProgress = true;
  currentGesture = "HELLO";
  gestureStep = 0;
  gestureStartTime = millis();
  lastGestureUpdate = millis();

  Serial.println("GREETING: Hello! Human detected - performing greeting gesture");
}

// Enhanced gesture execution to include hello greeting
void executeHelloGesture(unsigned long currentTime) {
  switch (gestureStep) {
    case 0: // Start - move right arm to wave position
      setRightArm(RIGHT_ARM_WAVE_POSITION);
      gestureStep = 1;
      lastGestureUpdate = currentTime;
      Serial.println("HELLO_STEP: Right arm raised");
      break;

    case 1: // Wait 600ms, then wave motion 1
      if (currentTime - lastGestureUpdate >= 600) {
        setRightArm(RIGHT_ARM_REST_POSITION + 10); // Slight wave motion
        gestureStep = 2;
        lastGestureUpdate = currentTime;
        Serial.println("HELLO_STEP: Wave motion 1");
      }
      break;

    case 2: // Wait 400ms, then wave motion 2
      if (currentTime - lastGestureUpdate >= 400) {
        setRightArm(RIGHT_ARM_WAVE_POSITION);
        gestureStep = 3;
        lastGestureUpdate = currentTime;
        Serial.println("HELLO_STEP: Wave motion 2");
      }
      break;

    case 3: // Wait 400ms, then wave motion 3
      if (currentTime - lastGestureUpdate >= 400) {
        setRightArm(RIGHT_ARM_REST_POSITION + 10);
        gestureStep = 4;
        lastGestureUpdate = currentTime;
        Serial.println("HELLO_STEP: Wave motion 3");
      }
      break;

    case 4: // Wait 400ms, then return to rest and finish
      if (currentTime - lastGestureUpdate >= 400) {
        setRightArm(RIGHT_ARM_REST_POSITION);
        gestureInProgress = false;
        currentGesture = "";
        gestureStep = 0;
        Serial.println("HELLO_COMPLETE: Greeting gesture completed - Right arm returned to rest (0°)");
      }
      break;
  }
}

// Smart Greeting Integration Functions
void performHandshakeGreeting() {
  // Stop any ongoing operations for handshake greeting
  stopAllOperations();

  // Start non-blocking handshake gesture (right arm handshake motion)
  gestureInProgress = true;
  currentGesture = "HANDSHAKE";
  gestureStep = 0;
  gestureStartTime = millis();
  lastGestureUpdate = millis();

  Serial.println("SMART_GREETING: Handshake gesture initiated - Welcome!");
}

void streamDistanceReading() {
  // Get current distance and stream it for real-time monitoring
  float distance = getUltrasonicDistance();

  // Send distance in a format that Android app can parse for real-time updates
  Serial.println("USB_DISTANCE_STREAM: " + String(distance) + " cm");
}

// Enhanced handshake gesture with smooth servo movement (like your example)
void executeHandshakeGesture(unsigned long currentTime) {
  Serial.println("HANDSHAKE_DEBUG: executeHandshakeGesture called - Step " + String(gestureStep));

  switch (gestureStep) {
    case 0: // Start - move right arm smoothly to handshake position (40°)
      Serial.println("HANDSHAKE_STEP_0: Starting handshake - moving to " + String(RIGHT_ARM_GREETING_POSITION) + "°");
      // Using moveRightArmSmoothly which now supports corrected direction for both arms
      moveRightArmSmoothly(RIGHT_ARM_GREETING_POSITION); // 40° for handshake (forward direction)
      gestureStep = 1;
      lastGestureUpdate = currentTime;
      Serial.println("HANDSHAKE_STEP_0: Right arm moved to handshake position, now holding for " + String(handshakeDuration) + "ms");
      break;

    case 1: // Hold handshake position for configured duration (configurable from Android)
      if (currentTime - lastGestureUpdate >= handshakeDuration) { // Use configurable duration
        Serial.println("HANDSHAKE_STEP_1: Hold duration completed, moving to step 2");
        gestureStep = 2;
        lastGestureUpdate = currentTime;
        Serial.println("HANDSHAKE_STEP: Handshake hold completed (" + String(handshakeDuration) + "ms), returning to rest");
      } else {
        // Show remaining time every 1 second
        unsigned long remaining = handshakeDuration - (currentTime - lastGestureUpdate);
        if (remaining % 1000 < 50) { // Print roughly every second
          Serial.println("HANDSHAKE_HOLD: " + String(remaining) + "ms remaining");
        }
      }
      break;

    case 2: // Return smoothly to rest position (0°)
      Serial.println("HANDSHAKE_STEP_2: Returning to rest position " + String(RIGHT_ARM_REST_POSITION) + "°");
      moveRightArmSmoothly(RIGHT_ARM_REST_POSITION); // 0° rest position
      gestureInProgress = false;
      currentGesture = "";
      gestureStep = 0;
      Serial.println("HANDSHAKE_COMPLETE: Smart Greeting handshake completed - Right arm returned to rest (0°)!");
      break;
  }
}
