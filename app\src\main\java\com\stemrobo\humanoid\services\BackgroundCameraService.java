package com.stemrobo.humanoid.services;

import android.app.Service;
import android.content.Intent;
import android.os.IBinder;
import android.os.Handler;
import android.os.Looper;
import android.graphics.Bitmap;
import android.graphics.ImageFormat;
import android.graphics.SurfaceTexture;
import android.hardware.camera2.*;
import android.hardware.camera2.params.StreamConfigurationMap;
import android.media.Image;
import android.media.ImageReader;
import android.util.Size;
import android.view.Surface;
import java.util.List;
import java.util.Arrays;
import java.nio.ByteBuffer;

// ML Kit imports for face detection
import com.google.mlkit.vision.common.InputImage;
import com.google.mlkit.vision.face.Face;
import com.google.mlkit.vision.face.FaceDetection;
import com.google.mlkit.vision.face.FaceDetector;
import com.google.mlkit.vision.face.FaceDetectorOptions;

/**
 * Background camera service for continuous face detection
 * Provides always-on face detection for Smart Greeting functionality
 * Works independently of UI fragments
 */
public class BackgroundCameraService extends Service {
    private static final String TAG = "BackgroundCameraService";
    
    // Service state
    private boolean isServiceRunning = false;
    private boolean isFaceDetectionActive = false;

    // Background processing
    private Handler backgroundHandler;
    private Runnable faceDetectionRunnable;

    // Camera components
    private CameraManager cameraManager;
    private CameraDevice cameraDevice;
    private CameraCaptureSession captureSession;
    private ImageReader imageReader;
    private String cameraId;

    // Face detection
    private FaceDetector faceDetector;

    // Detection settings
    private static final int FACE_DETECTION_INTERVAL = 500; // 500ms for face detection
    private static final int MAX_FACES_TO_TRACK = 5; // Maximum faces to track simultaneously
    private static final int IMAGE_WIDTH = 640;
    private static final int IMAGE_HEIGHT = 480;

    // Current detection results
    private int currentFaceCount = 0;
    private long lastDetectionTime = 0;

    // Smart Greeting integration
    private com.stemrobo.humanoid.behaviors.SmartGreetingManager smartGreetingManager;

    // Callback interface for face detection updates
    public interface FaceDetectionCallback {
        void onFaceCountUpdated(int faceCount);
        void onFaceDetectionStatusChanged(boolean isActive);
        void onServiceStatusChanged(boolean isRunning);
    }

    private FaceDetectionCallback callback;
    
    @Override
    public void onCreate() {
        super.onCreate();
        System.out.println(TAG + ": Background camera service created");

        // Initialize background handler
        backgroundHandler = new Handler(Looper.getMainLooper());

        // Initialize camera manager
        cameraManager = (CameraManager) getSystemService(CAMERA_SERVICE);

        // Initialize face detector
        initializeFaceDetector();

        // Initialize face detection runnable
        initializeFaceDetectionRunnable();

        // Initialize Smart Greeting Manager
        smartGreetingManager = com.stemrobo.humanoid.behaviors.SmartGreetingManager.getInstance();
        if (smartGreetingManager != null) {
            System.out.println(TAG + ": Smart Greeting Manager connected to background service");
        }

        isServiceRunning = true;
        System.out.println(TAG + ": Background camera service initialized successfully");
    }

    /**
     * Initialize ML Kit face detector
     */
    private void initializeFaceDetector() {
        try {
            FaceDetectorOptions options = new FaceDetectorOptions.Builder()
                .setPerformanceMode(FaceDetectorOptions.PERFORMANCE_MODE_FAST)
                .setLandmarkMode(FaceDetectorOptions.LANDMARK_MODE_NONE)
                .setClassificationMode(FaceDetectorOptions.CLASSIFICATION_MODE_NONE)
                .setMinFaceSize(0.1f)
                .enableTracking()
                .build();

            faceDetector = FaceDetection.getClient(options);
            System.out.println(TAG + ": Face detector initialized successfully");
        } catch (Exception e) {
            System.out.println(TAG + ": Error initializing face detector: " + e.getMessage());
        }
    }
    
    @Override
    public int onStartCommand(Intent intent, int flags, int startId) {
        if (intent != null) {
            String action = intent.getStringExtra("action");
            if ("START_FACE_DETECTION".equals(action)) {
                startBackgroundFaceDetection();
            } else if ("STOP_FACE_DETECTION".equals(action)) {
                stopBackgroundFaceDetection();
            }
        }
        
        // Return START_STICKY to restart service if killed
        return START_STICKY;
    }
    
    @Override
    public IBinder onBind(Intent intent) {
        return new BackgroundCameraBinder();
    }
    
    @Override
    public void onDestroy() {
        super.onDestroy();
        stopBackgroundFaceDetection();
        System.out.println(TAG + ": Background camera service destroyed");
    }
    
    /**
     * Start background face detection
     */
    public void startBackgroundFaceDetection() {
        if (isServiceRunning) {
            System.out.println(TAG + ": Background face detection already running");
            return;
        }
        
        isServiceRunning = true;
        isFaceDetectionActive = true;
        
        System.out.println(TAG + ": Starting background face detection");
        
        // Start continuous face detection
        if (backgroundHandler != null && faceDetectionRunnable != null) {
            backgroundHandler.post(faceDetectionRunnable);
        }
        
        // Notify callback
        if (callback != null) {
            callback.onServiceStatusChanged(true);
            callback.onFaceDetectionStatusChanged(true);
        }
        
        System.out.println(TAG + ": Background face detection started successfully");
    }
    
    /**
     * Stop background face detection
     */
    public void stopBackgroundFaceDetection() {
        if (!isServiceRunning) {
            return;
        }
        
        isServiceRunning = false;
        isFaceDetectionActive = false;
        
        System.out.println(TAG + ": Stopping background face detection");
        
        // Stop detection runnable
        if (backgroundHandler != null && faceDetectionRunnable != null) {
            backgroundHandler.removeCallbacks(faceDetectionRunnable);
        }
        
        // Reset detection results
        currentFaceCount = 0;
        lastDetectionTime = 0;
        
        // Notify callback
        if (callback != null) {
            callback.onFaceCountUpdated(0);
            callback.onFaceDetectionStatusChanged(false);
            callback.onServiceStatusChanged(false);
        }
        
        System.out.println(TAG + ": Background face detection stopped");
    }
    
    /**
     * Initialize face detection runnable
     */
    private void initializeFaceDetectionRunnable() {
        faceDetectionRunnable = new Runnable() {
            @Override
            public void run() {
                if (isServiceRunning && isFaceDetectionActive) {
                    performBackgroundFaceDetection();
                    
                    // Schedule next detection
                    backgroundHandler.postDelayed(this, FACE_DETECTION_INTERVAL);
                }
            }
        };
    }
    
    /**
     * Perform background face detection using ML Kit
     */
    private void performBackgroundFaceDetection() {
        try {
            long currentTime = System.currentTimeMillis();

            // Use existing camera service or VisionFragment for face detection
            // This integrates with the existing face detection infrastructure
            int detectedFaces = getExistingFaceCount();

            // Only update if face count changed or significant time passed
            if (detectedFaces != currentFaceCount ||
                (currentTime - lastDetectionTime) > 2000) { // Update every 2 seconds minimum

                currentFaceCount = detectedFaces;
                lastDetectionTime = currentTime;

                // Notify callback of face count update
                if (callback != null) {
                    callback.onFaceCountUpdated(currentFaceCount);
                }

                System.out.println(TAG + ": Face detection update - Count: " + currentFaceCount);
            }

        } catch (Exception e) {
            System.out.println(TAG + ": Error in background face detection: " + e.getMessage());
        }
    }

    /**
     * Get face count from existing face detection infrastructure
     */
    private int getExistingFaceCount() {
        try {
            // Try to get face count from VisionFragment or FaceDetectionManager
            // This leverages existing working face detection

            // Check if we can access the existing face detection manager
            com.stemrobo.humanoid.vision.FaceDetectionManager faceManager =
                com.stemrobo.humanoid.vision.FaceDetectionManager.getInstance();

            if (faceManager != null) {
                int faceCount = faceManager.getCurrentFaceCount();

                // ENHANCED: Notify SmartGreetingManager about face detection
                if (smartGreetingManager != null) {
                    // Since FaceDetectionManager doesn't expose Face objects directly,
                    // we'll use a simplified approach with face count
                    if (faceCount > 0) {
                        System.out.println(TAG + ": " + faceCount + " faces detected, notifying SmartGreetingManager");
                        // Create a simple notification that faces are present
                        // The SmartGreetingManager will handle distance checking
                        smartGreetingManager.onFaceCountUpdated(faceCount);
                    } else {
                        // No faces detected
                        smartGreetingManager.onFaceCountUpdated(0);
                    }
                }

                return faceCount;
            }

            // Fallback: return 0 if no face detection available
            return 0;

        } catch (Exception e) {
            System.out.println(TAG + ": Error getting existing face count: " + e.getMessage());
            return 0;
        }
    }
    

    
    /**
     * Set callback for face detection updates
     */
    public void setCallback(FaceDetectionCallback callback) {
        this.callback = callback;
    }
    
    /**
     * Get current service status
     */
    public boolean isRunning() {
        return isServiceRunning;
    }
    
    /**
     * Get current face detection status
     */
    public boolean isFaceDetectionActive() {
        return isFaceDetectionActive;
    }
    
    /**
     * Get current face count
     */
    public int getCurrentFaceCount() {
        return currentFaceCount;
    }
    
    /**
     * Update face detection interval
     */
    public void setFaceDetectionInterval(int intervalMs) {
        // This would update the detection interval
        System.out.println(TAG + ": Face detection interval updated to " + intervalMs + "ms");
    }
    
    /**
     * Manual face detection trigger (for testing)
     */
    public void triggerFaceDetection() {
        if (isServiceRunning && isFaceDetectionActive) {
            performBackgroundFaceDetection();
        }
    }
    
    /**
     * Get service statistics
     */
    public String getServiceStats() {
        return String.format("Service Running: %s, Face Detection Active: %s, Current Faces: %d, Last Detection: %d ms ago",
                           isServiceRunning, 
                           isFaceDetectionActive, 
                           currentFaceCount,
                           System.currentTimeMillis() - lastDetectionTime);
    }
    
    /**
     * Binder class for service binding
     */
    public class BackgroundCameraBinder extends android.os.Binder {
        public BackgroundCameraService getService() {
            return BackgroundCameraService.this;
        }
    }
}
