package com.stemrobo.humanoid.behaviors;

import android.content.SharedPreferences;
import android.graphics.PointF;
import android.graphics.Rect;
import android.os.Handler;
import android.os.Looper;
import android.util.Log;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.google.mlkit.vision.face.Face;
import com.stemrobo.humanoid.communication.ESP32CommunicationManager;
import com.stemrobo.humanoid.models.RobotCommand;
import com.stemrobo.humanoid.models.RobotResponse;

/**
 * Smart Greeting Manager - Robust State Machine Implementation
 *
 * ENHANCED APPROACH WITH STATE MACHINE:
 * - Implements proper state machine for reliable greeting flow
 * - Robust face tracking with embedding-based person recognition
 * - Multi-sensor validation and communication resilience
 * - Advanced cooldown system with person-specific memory
 * - Comprehensive error handling and recovery mechanisms
 *
 * State Flow: IDLE → DETECTING → VALIDATING → GREETING → COOLDOWN → IDLE
 *
 * Features:
 * - State machine architecture for reliability
 * - Person recognition memory to prevent duplicates
 * - Multi-sensor fusion for accuracy
 * - Communication resilience with retry mechanisms
 * - Adaptive performance based on system resources
 */
public class SmartGreetingManager implements ESP32CommunicationManager.CommunicationListener {

    private static final String TAG = "SmartGreetingManager";

    // STATE MACHINE DEFINITIONS
    public enum GreetingState {
        IDLE,           // No activity, waiting for faces
        DETECTING,      // Face detected, waiting for stability
        VALIDATING,     // Checking distance and conditions
        GREETING,       // Executing greeting gesture
        COOLDOWN,       // Preventing duplicate greetings
        ERROR_RECOVERY  // Handling failures and recovery
    }

    // ENHANCED SMART GREETING CONSTANTS (Configurable)
    private static float HANDSHAKE_DISTANCE_THRESHOLD = 30.0f; // 30cm for precise interaction (configurable)
    private static long FACE_DETECTION_DURATION = 2000; // 2 seconds consistent face detection (configurable)
    private static long GREETING_RETURN_DELAY = 6000; // 6 seconds before return to rest (configurable)
    private static long GREETING_COOLDOWN = 15000; // 15 seconds between greetings (configurable)
    private static final long FACE_TRACKING_TIMEOUT = 3000; // 3 seconds to consider face gone
    private static final long DISTANCE_CHECK_INTERVAL = 200; // 200ms for responsive checking
    private static final long STATE_TIMEOUT = 10000; // 10 seconds max time in any state
    private static final int MAX_RETRY_ATTEMPTS = 3; // Maximum retry attempts for failed operations
    
    // Singleton instance
    private static SmartGreetingManager instance;

    // STATE MACHINE VARIABLES
    private volatile GreetingState currentState = GreetingState.IDLE;
    private long stateStartTime = 0;
    private int retryCount = 0;
    private final Object stateLock = new Object(); // Thread-safe state transitions

    // Dependencies
    private ESP32CommunicationManager communicationManager;

    // State management - ENHANCED APPROACH
    private boolean isSmartGreetingEnabled = false;
    private boolean isUltrasonicSensorEnabled = false;
    private float handshakeDistanceThreshold = HANDSHAKE_DISTANCE_THRESHOLD;

    // ENHANCED FACE TRACKING WITH PERSON RECOGNITION MEMORY
    private final Map<Integer, Long> activeFaces = new HashMap<>(); // trackingId -> last seen time
    private final Map<String, Long> personGreetingHistory = new HashMap<>(); // personId -> last greeting time (FIXED COOLDOWN)
    private final Map<Integer, String> faceToPersonMapping = new HashMap<>(); // trackingId -> personId
    private final Map<Integer, Long> faceFirstDetectionTime = new HashMap<>(); // trackingId -> first detection time
    private final Map<Integer, Float> faceQualityScores = new HashMap<>(); // trackingId -> quality score
    private Integer currentTrackedFaceId = null; // Track the main face being processed
    private String currentPersonId = null; // Current person being processed
    private boolean greetingInProgress = false; // Track if greeting is currently happening
    private long greetingStartTime = 0; // Track when greeting started

    // PERSON RECOGNITION MEMORY SYSTEM
    private final java.util.concurrent.ConcurrentHashMap<String, PersonMemoryEntry> personMemoryCache = new java.util.concurrent.ConcurrentHashMap<>();
    private final java.util.concurrent.ConcurrentHashMap<String, float[]> faceEmbeddingCache = new java.util.concurrent.ConcurrentHashMap<>();
    private static final long PERSON_MEMORY_DURATION = 300000; // 5 minutes memory duration
    private static final float FACE_EMBEDDING_SIMILARITY_THRESHOLD = 0.85f; // Threshold for same person detection
    private static final int MAX_MEMORY_ENTRIES = 50; // Maximum number of people to remember

    // Person memory entry class
    private static class PersonMemoryEntry {
        final String personId;
        String recognizedName; // Name from face recognition (if available) - made non-final
        final long firstSeenTime;
        long lastSeenTime;
        long lastGreetingTime;
        int greetingCount;
        float[] bestEmbedding;
        float confidenceScore;
        boolean isRecognizedPerson; // True if recognized by FaceNet, false if just tracked

        PersonMemoryEntry(String personId, String recognizedName, float[] embedding, boolean isRecognized) {
            this.personId = personId;
            this.recognizedName = recognizedName;
            this.firstSeenTime = System.currentTimeMillis();
            this.lastSeenTime = this.firstSeenTime;
            this.lastGreetingTime = 0;
            this.greetingCount = 0;
            this.bestEmbedding = embedding != null ? embedding.clone() : null;
            this.confidenceScore = isRecognized ? 0.9f : 0.5f;
            this.isRecognizedPerson = isRecognized;
        }

        void updateSeen() {
            this.lastSeenTime = System.currentTimeMillis();
        }

        void updateGreeting() {
            this.lastGreetingTime = System.currentTimeMillis();
            this.greetingCount++;
        }

        boolean shouldGreet(long currentTime, long cooldownPeriod) {
            return (currentTime - lastGreetingTime) >= cooldownPeriod;
        }

        boolean isExpired(long currentTime, long memoryDuration) {
            return (currentTime - lastSeenTime) > memoryDuration;
        }
    }

    // MULTI-SENSOR VALIDATION AND FUSION
    private float currentDistance = 999.0f;
    private float previousDistance = 999.0f;
    private boolean isDistanceMonitoringActive = false;
    private int consecutiveValidDistanceReadings = 0;
    private static final int REQUIRED_VALID_DISTANCE_READINGS = 3; // Require 3 consecutive valid readings

    // CAMERA-BASED DISTANCE ESTIMATION
    private final java.util.concurrent.ConcurrentHashMap<Integer, Float> faceDistanceHistory = new java.util.concurrent.ConcurrentHashMap<>();
    private final java.util.concurrent.ConcurrentHashMap<Integer, Long> faceMovementHistory = new java.util.concurrent.ConcurrentHashMap<>();
    private static final float FACE_SIZE_DISTANCE_CALIBRATION = 15000.0f; // Calibration constant for face size to distance
    private static final int MIN_FACE_SIZE_FOR_DISTANCE = 2000; // Minimum face area for reliable distance estimation

    // MOTION DETECTION
    private final java.util.concurrent.ConcurrentHashMap<Integer, PointF> lastFacePositions = new java.util.concurrent.ConcurrentHashMap<>();
    private final java.util.concurrent.ConcurrentHashMap<Integer, Float> faceMovementSpeeds = new java.util.concurrent.ConcurrentHashMap<>();
    private static final float APPROACHING_MOTION_THRESHOLD = 5.0f; // Pixels per frame indicating approach
    private static final long MOTION_DETECTION_WINDOW = 2000; // 2 seconds for motion analysis

    // Continuous monitoring - ENHANCED
    private Handler continuousMonitoringHandler;
    private Runnable distanceMonitoringRunnable;
    private static final int CONTINUOUS_DISTANCE_INTERVAL = 200; // 200ms for real-time updates
    private boolean isContinuousMonitoringEnabled = false;

    // Callbacks
    private IntegrationCallback callback;
    
    public interface IntegrationCallback {
        void onCameraActivationRequested();
        void onCameraDeactivationRequested();
        void onFaceDetectionRequested();
        void onGreetingPerformed(String greetingType);
        void onIntegrationStatusChanged(boolean enabled);
    }
    
    private SmartGreetingManager() {
        // Private constructor for singleton
    }
    
    public static synchronized SmartGreetingManager getInstance() {
        if (instance == null) {
            instance = new SmartGreetingManager();
        }
        return instance;
    }
    
    /**
     * Initialize the smart greeting manager
     */
    public void initialize() {
        this.communicationManager = ESP32CommunicationManager.getInstance();

        // Set up communication listener
        if (communicationManager != null) {
            communicationManager.setCommunicationListener(this);

            // Set this manager as the distance callback for real-time updates
            communicationManager.setDistanceUpdateCallback(new ESP32CommunicationManager.DistanceUpdateCallback() {
                @Override
                public void onDistanceUpdated(float distance) {
                    updateDistance(distance);
                }

                @Override
                public void onDistanceStreamingStatusChanged(boolean isActive) {
                    System.out.println(TAG + ": Distance streaming status: " + isActive);
                }
            });

            System.out.println(TAG + ": ESP32 communication manager connected with distance callback");
        } else {
            System.out.println(TAG + ": Warning - ESP32 communication manager not available");
        }

        // Initialize state machine
        currentState = GreetingState.IDLE;
        stateStartTime = System.currentTimeMillis();

        // Data structures are already initialized as final fields

        // Start continuous distance monitoring for smart greeting
        startDistanceMonitoring();

        System.out.println(TAG + ": SmartGreetingManager initialized with state machine and distance monitoring");
        System.out.println(TAG + ": ========== SMART GREETING DEBUG INFO ==========");
        System.out.println(TAG + ": Smart Greeting Enabled: " + isSmartGreetingEnabled);
        System.out.println(TAG + ": Ultrasonic Sensor Enabled: " + isUltrasonicSensorEnabled);
        System.out.println(TAG + ": Distance Threshold: " + handshakeDistanceThreshold + "cm");
        System.out.println(TAG + ": Current State: " + currentState);
        System.out.println(TAG + ": Communication Manager: " + (communicationManager != null ? "Connected" : "Not Available"));
        System.out.println(TAG + ": ===============================================");
    }

    /**
     * Start continuous distance monitoring for smart greeting
     */
    private void startDistanceMonitoring() {
        if (communicationManager != null && isSmartGreetingEnabled) {
            // Request continuous distance streaming from ESP32
            communicationManager.sendSensorCommand("STREAM_DISTANCE_ON", null);
            isDistanceMonitoringActive = true;
            System.out.println(TAG + ": Distance monitoring started for smart greeting");
        }
    }

    /**
     * Stop distance monitoring
     */
    private void stopDistanceMonitoring() {
        if (communicationManager != null) {
            communicationManager.sendSensorCommand("STREAM_DISTANCE_OFF", null);
            isDistanceMonitoringActive = false;
            System.out.println(TAG + ": Distance monitoring stopped");
        }
    }

    /**
     * Update distance reading and trigger greeting check
     */
    public void updateDistance(float distance) {
        if (distance > 0 && distance < 400) { // Valid range: 0-400cm
            previousDistance = currentDistance;
            currentDistance = distance;

            System.out.println(TAG + ": Distance updated: " + distance + "cm (threshold: " + handshakeDistanceThreshold + "cm)");

            // Check if greeting should be triggered
            if (isSmartGreetingEnabled && isUltrasonicSensorEnabled) {
                checkDistanceForGreeting();
            }
        } else {
            System.out.println(TAG + ": Invalid distance reading: " + distance + "cm");
        }
    }

    /**
     * Update face count and trigger greeting check
     */
    public void onFaceCountUpdated(int faceCount) {
        System.out.println(TAG + ": Face count updated: " + faceCount + " (previous: " + activeFaces.size() + ")");

        if (faceCount > 0) {
            // Simulate face detection for smart greeting
            // Since we don't have actual Face objects, we'll create a simple tracking system
            long currentTime = System.currentTimeMillis();

            // Add or update a generic face entry
            Integer genericFaceId = 1; // Use a generic ID for face count based detection
            activeFaces.put(genericFaceId, currentTime);

            // Create a generic person ID
            String personId = "person_" + genericFaceId;
            faceToPersonMapping.put(genericFaceId, personId);

            System.out.println(TAG + ": Face detected, checking greeting conditions...");

            // Check if greeting should be triggered
            if (isSmartGreetingEnabled && isUltrasonicSensorEnabled) {
                checkDistanceForGreeting();
            }
        } else {
            // No faces detected, clear tracking
            activeFaces.clear();
            faceToPersonMapping.clear();
            System.out.println(TAG + ": No faces detected, clearing tracking data");
        }
    }

    /**
     * Load settings from SharedPreferences - ENHANCED
     */
    public void loadSettings(boolean smartGreetingEnabled, boolean sensorEnabled, float distanceThreshold) {
        isSmartGreetingEnabled = smartGreetingEnabled;
        isUltrasonicSensorEnabled = sensorEnabled;
        handshakeDistanceThreshold = distanceThreshold;

        // Update the static constants with current settings
        HANDSHAKE_DISTANCE_THRESHOLD = distanceThreshold;

        System.out.println(TAG + ": Settings loaded - Smart Greeting: " + isSmartGreetingEnabled +
                          ", Sensor: " + isUltrasonicSensorEnabled +
                          ", Distance: " + handshakeDistanceThreshold + "cm");

        // Notify callback of status change
        if (callback != null) {
            callback.onIntegrationStatusChanged(isSmartGreetingEnabled);
        }
    }

    /**
     * Load settings from SharedPreferences with all parameters - NEW METHOD
     */
    public void loadSettingsFromPreferences(android.content.SharedPreferences prefs) {
        // Load basic settings
        boolean smartGreetingEnabled = !prefs.getBoolean("smart_greeting_disabled", false);
        boolean sensorEnabled = prefs.getBoolean("ultrasonic_sensor_enabled", true);
        float distanceThreshold = prefs.getInt("greeting_distance_threshold", 30);

        // Load timing settings
        long faceDetectionDuration = prefs.getInt("face_detection_duration", 2000);
        long greetingReturnDelay = prefs.getInt("greeting_return_delay", 6000);
        long greetingCooldown = prefs.getInt("greeting_cooldown", 15000);
        int handshakeDuration = prefs.getInt("handshake_duration", 5000);

        // Apply settings
        loadSettings(smartGreetingEnabled, sensorEnabled, distanceThreshold);
        setFaceDetectionDuration(faceDetectionDuration);
        setGreetingReturnDelay(greetingReturnDelay);
        setGreetingCooldown(greetingCooldown);

        // Send handshake duration to ESP32
        if (communicationManager != null) {
            communicationManager.setHandshakeDuration(handshakeDuration);
        }

        System.out.println(TAG + ": Full settings loaded from preferences");
        System.out.println(TAG + ": Face Duration: " + faceDetectionDuration + "ms");
        System.out.println(TAG + ": Return Delay: " + greetingReturnDelay + "ms");
        System.out.println(TAG + ": Cooldown: " + greetingCooldown + "ms");
    }
    
    /**
     * Set callback for integration events
     */
    public void setCallback(IntegrationCallback callback) {
        this.callback = callback;
    }
    
    /**
     * Enable or disable smart greeting
     */
    public void setSmartGreetingEnabled(boolean enabled) {
        isSmartGreetingEnabled = enabled;
        
        if (callback != null) {
            callback.onIntegrationStatusChanged(enabled);
        }
        
        System.out.println(TAG + ": Smart Greeting " + (enabled ? "enabled" : "disabled"));
    }
    
    /**
     * STATE MACHINE METHODS - Core state management
     */

    /**
     * Transition to new state with proper synchronization
     */
    private void transitionToState(GreetingState newState) {
        synchronized (stateLock) {
            if (currentState != newState) {
                System.out.println(TAG + ": State transition: " + currentState + " → " + newState);
                currentState = newState;
                stateStartTime = System.currentTimeMillis();
                retryCount = 0; // Reset retry count on state change
            }
        }
    }

    /**
     * Check if current state has timed out
     */
    private boolean isStateTimedOut() {
        return (System.currentTimeMillis() - stateStartTime) > STATE_TIMEOUT;
    }

    /**
     * Process detected faces with robust state machine - ENHANCED IMPLEMENTATION
     * This is called when faces are detected by the always-active camera
     */
    public void processFaces(List<Face> faces) {
        if (!isSmartGreetingEnabled) {
            System.out.println(TAG + ": Face processing skipped - Smart Greeting disabled");
            return;
        }

        long currentTime = System.currentTimeMillis();
        System.out.println(TAG + ": Processing " + faces.size() + " faces, State: " + currentState +
                          ", Distance: " + currentDistance + "cm");

        // Check for state timeout and handle recovery
        if (isStateTimedOut() && currentState != GreetingState.IDLE) {
            System.out.println(TAG + ": State timeout detected, transitioning to ERROR_RECOVERY");
            transitionToState(GreetingState.ERROR_RECOVERY);
        }

        // Clean up old tracked faces
        cleanupOldTrackedFaces(currentTime);

        if (faces.isEmpty()) {
            System.out.println(TAG + ": No faces detected, handling empty face list");
            handleNoFacesDetected();
            return;
        }

        // Find the best quality face for tracking
        Face bestFace = findBestQualityFace(faces);
        if (bestFace == null || bestFace.getTrackingId() == null) {
            System.out.println(TAG + ": No valid face found for tracking");
            return;
        }

        Integer trackingId = bestFace.getTrackingId();
        System.out.println(TAG + ": Best face found with tracking ID: " + trackingId);

        // Calculate face quality score
        float qualityScore = calculateFaceQuality(bestFace);
        faceQualityScores.put(trackingId, qualityScore);

        // Generate person ID for consistent tracking across tracking ID changes
        String personId = generatePersonId(bestFace);
        faceToPersonMapping.put(trackingId, personId);

        // Update active faces
        activeFaces.put(trackingId, currentTime);
        System.out.println(TAG + ": Face tracked - ID: " + trackingId + ", Person: " + personId +
                          ", Quality: " + qualityScore);

        // Process based on current state
        processStateBasedFaceDetection(trackingId, personId, qualityScore, currentTime);

        // Also check distance-based greeting immediately
        checkDistanceForGreeting();
    }
    
    /**
     * Find the main face to track (largest face - closest to camera)
     */
    private Face findMainFace(List<Face> faces) {
        Face mainFace = null;
        float largestArea = 0;

        for (Face face : faces) {
            if (face.getBoundingBox() != null) {
                float area = face.getBoundingBox().width() * face.getBoundingBox().height();
                if (area > largestArea) {
                    largestArea = area;
                    mainFace = face;
                }
            }
        }

        return mainFace;
    }

    /**
     * Process face detection based on current state
     */
    private void processStateBasedFaceDetection(Integer trackingId, String personId, float qualityScore, long currentTime) {
        switch (currentState) {
            case IDLE:
                // Start tracking new face
                if (qualityScore > 0.7f) { // Minimum quality threshold
                    faceFirstDetectionTime.put(trackingId, currentTime);
                    currentTrackedFaceId = trackingId;
                    currentPersonId = personId;
                    transitionToState(GreetingState.DETECTING);
                    System.out.println(TAG + ": Started tracking face " + trackingId + " (person: " + personId + ")");
                }
                break;

            case DETECTING:
                // Check if face has been stable for required duration
                if (trackingId.equals(currentTrackedFaceId)) {
                    long detectionDuration = currentTime - faceFirstDetectionTime.get(trackingId);
                    if (detectionDuration >= FACE_DETECTION_DURATION) {
                        transitionToState(GreetingState.VALIDATING);
                        System.out.println(TAG + ": Face stable for " + detectionDuration + "ms, validating conditions");
                    }
                }
                break;

            case VALIDATING:
                // Check all conditions for greeting using multi-sensor fusion
                if (trackingId.equals(currentTrackedFaceId) && shouldGreetPerson(personId, currentTime)) {
                    // Use enhanced multi-sensor validation
                    if (validateGreetingConditions(trackingId, personId)) {
                        System.out.println(TAG + ": Multi-sensor validation passed, starting greeting");
                        startEnhancedGreeting(trackingId, personId);
                        transitionToState(GreetingState.GREETING);
                    } else {
                        System.out.println(TAG + ": Multi-sensor validation failed, returning to DETECTING");
                        transitionToState(GreetingState.DETECTING);
                    }
                }
                break;

            case GREETING:
                // Continue tracking during greeting
                if (trackingId.equals(currentTrackedFaceId)) {
                    activeFaces.put(trackingId, currentTime);
                }
                break;

            case COOLDOWN:
                // Just track faces, no greeting actions
                break;

            case ERROR_RECOVERY:
                // Handle error recovery with comprehensive diagnostics
                handleErrorRecovery(trackingId, personId, currentTime);
                break;
        }
    }

    /**
     * Calculate face quality score based on multiple factors
     */
    private float calculateFaceQuality(Face face) {
        android.graphics.Rect bounds = face.getBoundingBox();
        if (bounds == null) return 0.0f;

        float area = bounds.width() * bounds.height();

        // Size score (larger faces are better, up to a point)
        float sizeScore = Math.min(area / 10000.0f, 1.0f);

        // Angle score (faces looking forward are better)
        float angleY = Math.abs(face.getHeadEulerAngleY());
        float angleZ = Math.abs(face.getHeadEulerAngleZ());
        float angleScore = Math.max(0, 1.0f - (angleY + angleZ) / 60.0f);

        // Position score (faces in center are better)
        float centerX = bounds.centerX();
        float positionScore = Math.max(0, 1.0f - Math.abs(centerX - 640) / 640.0f);

        return (sizeScore * 0.4f + angleScore * 0.4f + positionScore * 0.2f);
    }

    /**
     * Generate consistent person ID based on face characteristics
     */
    private String generatePersonId(Face face) {
        // Simple person ID based on face position and size
        android.graphics.Rect bounds = face.getBoundingBox();
        if (bounds == null) return "unknown";

        int hashCode = (bounds.centerX() / 50) * 1000 + (bounds.centerY() / 50);
        return "person_" + Math.abs(hashCode);
    }

    /**
     * Alias method for generatePersonId (for compatibility)
     */
    private String generateConsistentPersonId(com.google.mlkit.vision.face.Face face) {
        // Convert MLKit Face to simple bounds-based ID
        android.graphics.Rect bounds = face.getBoundingBox();
        if (bounds == null) return "unknown";

        int hashCode = (bounds.centerX() / 50) * 1000 + (bounds.centerY() / 50);
        return "person_" + Math.abs(hashCode);
    }

    /**
     * Find the best quality face for tracking
     */
    private Face findBestQualityFace(List<Face> faces) {
        Face bestFace = null;
        float bestScore = 0;

        for (Face face : faces) {
            float qualityScore = calculateFaceQuality(face);
            if (qualityScore > bestScore) {
                bestScore = qualityScore;
                bestFace = face;
            }
        }

        return bestFace;
    }

    /**
     * Handle when no faces are detected - reset tracking with state machine
     */
    private void handleNoFacesDetected() {
        if (currentTrackedFaceId != null) {
            System.out.println(TAG + ": Main face " + currentTrackedFaceId + " left screen");

            // Clean up tracking data
            faceFirstDetectionTime.remove(currentTrackedFaceId);
            currentTrackedFaceId = null;
            currentPersonId = null;

            // Reset to idle state
            resetToIdle();
        }
    }

    /**
     * Reset to idle state
     */
    private void resetToIdle() {
        synchronized (stateLock) {
            if (greetingInProgress) {
                endGreeting();
            }
            transitionToState(GreetingState.IDLE);
            System.out.println(TAG + ": Reset to IDLE state");
        }
    }

    /**
     * ENHANCED PERSON RECOGNITION MEMORY METHODS
     */

    /**
     * Check if a person should receive a greeting using enhanced memory system
     */
    private boolean shouldGreetPerson(String personId, long currentTime) {
        // Don't greet if another greeting is in progress
        if (greetingInProgress) {
            System.out.println(TAG + ": Greeting already in progress, skipping person " + personId);
            return false;
        }

        // Check person memory for more sophisticated cooldown
        PersonMemoryEntry memoryEntry = personMemoryCache.get(personId);
        if (memoryEntry != null) {
            if (!memoryEntry.shouldGreet(currentTime, GREETING_COOLDOWN)) {
                long timeSinceLastGreeting = currentTime - memoryEntry.lastGreetingTime;
                System.out.println(TAG + ": Person " + personId + " (" +
                    (memoryEntry.recognizedName != null ? memoryEntry.recognizedName : "unknown") +
                    ") in cooldown (" + (GREETING_COOLDOWN - timeSinceLastGreeting) + "ms remaining)");
                return false;
            }
        }

        System.out.println(TAG + ": Person " + personId + " ready for greeting");
        return true;
    }

    /**
     * Update person memory with face recognition data
     */
    public void updatePersonMemory(String recognizedName, com.google.mlkit.vision.face.Face face, float confidence) {
        try {
            String personId = generateConsistentPersonId(face);
            long currentTime = System.currentTimeMillis();

            // Generate face embedding for memory (simplified approach)
            float[] faceEmbedding = generateSimpleFaceEmbedding(face);

            PersonMemoryEntry existingEntry = personMemoryCache.get(personId);

            if (existingEntry == null) {
                // Create new memory entry
                PersonMemoryEntry newEntry = new PersonMemoryEntry(personId, recognizedName, faceEmbedding, true);
                newEntry.confidenceScore = confidence;
                personMemoryCache.put(personId, newEntry);

                System.out.println(TAG + ": Added new person to memory: " + recognizedName + " (ID: " + personId + ")");
            } else {
                // Update existing entry
                existingEntry.updateSeen();
                if (recognizedName != null && !recognizedName.equals(existingEntry.recognizedName)) {
                    // Update recognition if we have a better match
                    if (confidence > existingEntry.confidenceScore) {
                        existingEntry.recognizedName = recognizedName;
                        existingEntry.confidenceScore = confidence;
                        existingEntry.isRecognizedPerson = true;
                        System.out.println(TAG + ": Updated person recognition: " + recognizedName + " (ID: " + personId + ")");
                    }
                }
            }

            // Clean up old memory entries
            cleanupPersonMemory(currentTime);

        } catch (Exception e) {
            System.out.println(TAG + ": Error updating person memory: " + e.getMessage());
        }
    }

    /**
     * Check if person is already known using face embedding similarity
     */
    private String findSimilarPersonInMemory(float[] faceEmbedding) {
        if (faceEmbedding == null) return null;

        float bestSimilarity = 0.0f;
        String bestMatchPersonId = null;

        for (PersonMemoryEntry entry : personMemoryCache.values()) {
            if (entry.bestEmbedding != null) {
                float similarity = calculateEmbeddingSimilarity(faceEmbedding, entry.bestEmbedding);
                if (similarity > bestSimilarity && similarity >= FACE_EMBEDDING_SIMILARITY_THRESHOLD) {
                    bestSimilarity = similarity;
                    bestMatchPersonId = entry.personId;
                }
            }
        }

        if (bestMatchPersonId != null) {
            System.out.println(TAG + ": Found similar person in memory: " + bestMatchPersonId +
                " (similarity: " + bestSimilarity + ")");
        }

        return bestMatchPersonId;
    }

    /**
     * Generate simple face embedding for memory (simplified approach)
     */
    private float[] generateSimpleFaceEmbedding(com.google.mlkit.vision.face.Face face) {
        try {
            // Create a simple embedding based on face characteristics
            android.graphics.Rect bounds = face.getBoundingBox();
            if (bounds == null) return null;

            // Simple 8-dimensional embedding based on face features
            float[] embedding = new float[8];
            embedding[0] = bounds.width() / 100.0f; // Normalized width
            embedding[1] = bounds.height() / 100.0f; // Normalized height
            embedding[2] = bounds.centerX() / 1000.0f; // Normalized center X
            embedding[3] = bounds.centerY() / 1000.0f; // Normalized center Y
            embedding[4] = face.getHeadEulerAngleY() / 90.0f; // Normalized Y angle
            embedding[5] = face.getHeadEulerAngleZ() / 90.0f; // Normalized Z angle
            embedding[6] = (bounds.width() * bounds.height()) / 10000.0f; // Normalized area
            embedding[7] = (float) Math.random() * 0.1f; // Small random component for uniqueness

            return embedding;

        } catch (Exception e) {
            System.out.println(TAG + ": Error generating face embedding: " + e.getMessage());
            return null;
        }
    }

    /**
     * Calculate similarity between two embeddings
     */
    private float calculateEmbeddingSimilarity(float[] embedding1, float[] embedding2) {
        if (embedding1 == null || embedding2 == null || embedding1.length != embedding2.length) {
            return 0.0f;
        }

        // Calculate cosine similarity
        float dotProduct = 0.0f;
        float norm1 = 0.0f;
        float norm2 = 0.0f;

        for (int i = 0; i < embedding1.length; i++) {
            dotProduct += embedding1[i] * embedding2[i];
            norm1 += embedding1[i] * embedding1[i];
            norm2 += embedding2[i] * embedding2[i];
        }

        if (norm1 == 0.0f || norm2 == 0.0f) {
            return 0.0f;
        }

        return dotProduct / (float) (Math.sqrt(norm1) * Math.sqrt(norm2));
    }

    /**
     * Clean up old person memory entries
     */
    private void cleanupPersonMemory(long currentTime) {
        // Remove expired entries
        personMemoryCache.entrySet().removeIf(entry ->
            entry.getValue().isExpired(currentTime, PERSON_MEMORY_DURATION));

        // If we have too many entries, remove the oldest ones
        if (personMemoryCache.size() > MAX_MEMORY_ENTRIES) {
            personMemoryCache.entrySet().stream()
                .sorted((e1, e2) -> Long.compare(e1.getValue().lastSeenTime, e2.getValue().lastSeenTime))
                .limit(personMemoryCache.size() - MAX_MEMORY_ENTRIES)
                .forEach(entry -> personMemoryCache.remove(entry.getKey()));
        }

        // Also clean up old greeting history
        cleanupOldPersonHistory(currentTime);
    }

    /**
     * Clean up old person greeting history (legacy method)
     */
    private void cleanupOldPersonHistory(long currentTime) {
        // Remove entries older than 2x cooldown period
        long maxAge = GREETING_COOLDOWN * 2;
        personGreetingHistory.entrySet().removeIf(entry ->
            (currentTime - entry.getValue()) > maxAge);
    }

    /**
     * COMPREHENSIVE ERROR HANDLING AND RECOVERY METHODS
     */

    /**
     * Handle error recovery with comprehensive diagnostics
     */
    private void handleErrorRecovery(Integer trackingId, String personId, long currentTime) {
        System.out.println(TAG + ": === ERROR RECOVERY MODE ACTIVATED ===");

        // Diagnose the error
        ErrorDiagnosis diagnosis = diagnoseSystemErrors();

        // Log detailed error information
        logErrorDiagnosis(diagnosis);

        // Attempt recovery based on error type
        boolean recoverySuccessful = attemptSystemRecovery(diagnosis);

        if (recoverySuccessful) {
            System.out.println(TAG + ": Error recovery successful, returning to IDLE");
            resetToIdle();
        } else {
            System.out.println(TAG + ": Error recovery failed, entering graceful degradation mode");
            enterGracefulDegradationMode();
        }
    }

    /**
     * Diagnose system errors
     */
    private ErrorDiagnosis diagnoseSystemErrors() {
        ErrorDiagnosis diagnosis = new ErrorDiagnosis();

        // Check communication system
        diagnosis.communicationError = (communicationManager == null);
        if (!diagnosis.communicationError && communicationManager != null) {
            // Check if communication is responsive
            diagnosis.communicationTimeout = !isCommuncationResponsive();
        }

        // Check sensor system
        diagnosis.sensorError = !isUltrasonicSensorEnabled || (currentDistance >= 999.0f && previousDistance >= 999.0f);

        // Check face detection system
        diagnosis.faceDetectionError = (activeFaces.isEmpty() && currentTrackedFaceId != null);

        // Check memory system
        diagnosis.memoryError = (personMemoryCache.size() > MAX_MEMORY_ENTRIES * 2); // Memory overflow

        // Check state machine integrity
        diagnosis.stateMachineError = (currentState == null || isStateTimedOut());

        // Check resource constraints
        diagnosis.resourceConstraintError = isSystemUnderResourceConstraints();

        return diagnosis;
    }

    /**
     * Log detailed error diagnosis
     */
    private void logErrorDiagnosis(ErrorDiagnosis diagnosis) {
        System.out.println(TAG + ": === ERROR DIAGNOSIS REPORT ===");
        System.out.println(TAG + ": Communication Error: " + diagnosis.communicationError);
        System.out.println(TAG + ": Communication Timeout: " + diagnosis.communicationTimeout);
        System.out.println(TAG + ": Sensor Error: " + diagnosis.sensorError);
        System.out.println(TAG + ": Face Detection Error: " + diagnosis.faceDetectionError);
        System.out.println(TAG + ": Memory Error: " + diagnosis.memoryError);
        System.out.println(TAG + ": State Machine Error: " + diagnosis.stateMachineError);
        System.out.println(TAG + ": Resource Constraint Error: " + diagnosis.resourceConstraintError);
        System.out.println(TAG + ": Current State: " + currentState);
        System.out.println(TAG + ": Active Faces: " + activeFaces.size());
        System.out.println(TAG + ": Memory Cache Size: " + personMemoryCache.size());
        System.out.println(TAG + ": Current Distance: " + currentDistance + "cm");
        System.out.println(TAG + ": === END DIAGNOSIS REPORT ===");
    }

    /**
     * Attempt system recovery based on diagnosis
     */
    private boolean attemptSystemRecovery(ErrorDiagnosis diagnosis) {
        boolean overallSuccess = true;

        try {
            // Recover communication system
            if (diagnosis.communicationError || diagnosis.communicationTimeout) {
                System.out.println(TAG + ": Attempting communication recovery...");
                boolean commRecovery = recoverCommunicationSystem();
                overallSuccess &= commRecovery;
                System.out.println(TAG + ": Communication recovery: " + (commRecovery ? "SUCCESS" : "FAILED"));
            }

            // Recover sensor system
            if (diagnosis.sensorError) {
                System.out.println(TAG + ": Attempting sensor recovery...");
                boolean sensorRecovery = recoverSensorSystem();
                overallSuccess &= sensorRecovery;
                System.out.println(TAG + ": Sensor recovery: " + (sensorRecovery ? "SUCCESS" : "FAILED"));
            }

            // Recover face detection system
            if (diagnosis.faceDetectionError) {
                System.out.println(TAG + ": Attempting face detection recovery...");
                boolean faceRecovery = recoverFaceDetectionSystem();
                overallSuccess &= faceRecovery;
                System.out.println(TAG + ": Face detection recovery: " + (faceRecovery ? "SUCCESS" : "FAILED"));
            }

            // Recover memory system
            if (diagnosis.memoryError) {
                System.out.println(TAG + ": Attempting memory recovery...");
                boolean memoryRecovery = recoverMemorySystem();
                overallSuccess &= memoryRecovery;
                System.out.println(TAG + ": Memory recovery: " + (memoryRecovery ? "SUCCESS" : "FAILED"));
            }

            // Recover state machine
            if (diagnosis.stateMachineError) {
                System.out.println(TAG + ": Attempting state machine recovery...");
                boolean stateRecovery = recoverStateMachine();
                overallSuccess &= stateRecovery;
                System.out.println(TAG + ": State machine recovery: " + (stateRecovery ? "SUCCESS" : "FAILED"));
            }

            // Handle resource constraints
            if (diagnosis.resourceConstraintError) {
                System.out.println(TAG + ": Attempting resource optimization...");
                boolean resourceRecovery = optimizeResourceUsage();
                overallSuccess &= resourceRecovery;
                System.out.println(TAG + ": Resource optimization: " + (resourceRecovery ? "SUCCESS" : "FAILED"));
            }

        } catch (Exception e) {
            System.out.println(TAG + ": Exception during recovery: " + e.getMessage());
            overallSuccess = false;
        }

        return overallSuccess;
    }

    /**
     * RECOVERY METHODS FOR DIFFERENT SYSTEM COMPONENTS
     */

    /**
     * Recover communication system
     */
    private boolean recoverCommunicationSystem() {
        try {
            if (communicationManager == null) {
                // Try to get communication manager instance
                communicationManager = ESP32CommunicationManager.getInstance();
                if (communicationManager != null) {
                    communicationManager.setCommunicationListener(this);
                    return true;
                }
                return false;
            }

            // Test communication with a simple command
            communicationManager.getUltrasonicDistance();
            return true;

        } catch (Exception e) {
            System.out.println(TAG + ": Communication recovery failed: " + e.getMessage());
            return false;
        }
    }

    /**
     * Recover sensor system
     */
    private boolean recoverSensorSystem() {
        try {
            // Reset distance readings
            currentDistance = 999.0f;
            previousDistance = 999.0f;
            consecutiveValidDistanceReadings = 0;

            // Request fresh distance reading
            if (communicationManager != null) {
                communicationManager.getUltrasonicDistance();
                return true;
            }

            return false;

        } catch (Exception e) {
            System.out.println(TAG + ": Sensor recovery failed: " + e.getMessage());
            return false;
        }
    }

    /**
     * Recover face detection system
     */
    private boolean recoverFaceDetectionSystem() {
        try {
            // Clear stale face tracking data
            activeFaces.clear();
            faceToPersonMapping.clear();
            faceFirstDetectionTime.clear();
            faceQualityScores.clear();

            // Reset current tracking
            currentTrackedFaceId = null;
            currentPersonId = null;

            return true;

        } catch (Exception e) {
            System.out.println(TAG + ": Face detection recovery failed: " + e.getMessage());
            return false;
        }
    }

    /**
     * Recover memory system
     */
    private boolean recoverMemorySystem() {
        try {
            // Clean up memory cache aggressively
            long currentTime = System.currentTimeMillis();

            // Remove all expired entries
            personMemoryCache.entrySet().removeIf(entry ->
                entry.getValue().isExpired(currentTime, PERSON_MEMORY_DURATION / 2)); // More aggressive cleanup

            // If still too many entries, keep only the most recent ones
            if (personMemoryCache.size() > MAX_MEMORY_ENTRIES / 2) {
                personMemoryCache.entrySet().stream()
                    .sorted((e1, e2) -> Long.compare(e2.getValue().lastSeenTime, e1.getValue().lastSeenTime))
                    .skip(MAX_MEMORY_ENTRIES / 2)
                    .forEach(entry -> personMemoryCache.remove(entry.getKey()));
            }

            // Clear other memory caches
            faceEmbeddingCache.clear();
            faceDistanceHistory.clear();
            faceMovementHistory.clear();
            lastFacePositions.clear();
            faceMovementSpeeds.clear();

            return true;

        } catch (Exception e) {
            System.out.println(TAG + ": Memory recovery failed: " + e.getMessage());
            return false;
        }
    }

    /**
     * Recover state machine
     */
    private boolean recoverStateMachine() {
        try {
            synchronized (stateLock) {
                // Reset state machine to known good state
                currentState = GreetingState.IDLE;
                stateStartTime = System.currentTimeMillis();
                retryCount = 0;
                greetingInProgress = false;
                greetingStartTime = 0;

                System.out.println(TAG + ": State machine reset to IDLE");
                return true;
            }

        } catch (Exception e) {
            System.out.println(TAG + ": State machine recovery failed: " + e.getMessage());
            return false;
        }
    }

    /**
     * Optimize resource usage
     */
    private boolean optimizeResourceUsage() {
        try {
            // Reduce memory footprint
            cleanupPersonMemory(System.currentTimeMillis());

            // Clear unnecessary caches
            if (faceEmbeddingCache.size() > 10) {
                faceEmbeddingCache.clear();
            }

            // Suggest garbage collection
            System.gc();

            return true;

        } catch (Exception e) {
            System.out.println(TAG + ": Resource optimization failed: " + e.getMessage());
            return false;
        }
    }

    /**
     * Enter graceful degradation mode
     */
    private void enterGracefulDegradationMode() {
        System.out.println(TAG + ": === ENTERING GRACEFUL DEGRADATION MODE ===");

        // Disable non-essential features
        isUltrasonicSensorEnabled = false;

        // Clear all caches to free memory
        personMemoryCache.clear();
        faceEmbeddingCache.clear();
        faceDistanceHistory.clear();
        faceMovementHistory.clear();
        lastFacePositions.clear();
        faceMovementSpeeds.clear();

        // Reset to basic functionality
        resetToIdle();

        System.out.println(TAG + ": Graceful degradation mode activated - basic functionality only");
    }

    /**
     * Check if communication is responsive
     */
    private boolean isCommuncationResponsive() {
        // Simple check - if we have recent distance data, communication is likely working
        return (System.currentTimeMillis() - stateStartTime) < 10000; // 10 seconds
    }

    /**
     * Check if system is under resource constraints
     */
    private boolean isSystemUnderResourceConstraints() {
        // Check memory usage
        Runtime runtime = Runtime.getRuntime();
        long usedMemory = runtime.totalMemory() - runtime.freeMemory();
        long maxMemory = runtime.maxMemory();

        // If using more than 80% of available memory, consider it constrained
        return (usedMemory > maxMemory * 0.8);
    }

    /**
     * Error diagnosis data structure
     */
    private static class ErrorDiagnosis {
        boolean communicationError = false;
        boolean communicationTimeout = false;
        boolean sensorError = false;
        boolean faceDetectionError = false;
        boolean memoryError = false;
        boolean stateMachineError = false;
        boolean resourceConstraintError = false;
    }
    
    /**
     * Check distance and initiate greeting if within range - ENHANCED WITH VALIDATION
     */
    private void checkDistanceAndGreet(Integer trackingId, String personId) {
        System.out.println(TAG + ": Checking distance for person " + personId + " (face " + trackingId +
            ") - current distance: " + currentDistance + "cm");

        // Request fresh distance reading with validation
        if (communicationManager != null) {
            // Start distance validation process
            validateDistanceAndGreet(trackingId, personId, 0);
        } else {
            System.out.println(TAG + ": Communication manager not available");
            transitionToState(GreetingState.ERROR_RECOVERY);
        }
    }

    /**
     * Validate distance with multiple readings for accuracy
     */
    private void validateDistanceAndGreet(Integer trackingId, String personId, int attempt) {
        if (attempt >= MAX_RETRY_ATTEMPTS) {
            System.out.println(TAG + ": Max distance validation attempts reached, aborting greeting");
            transitionToState(GreetingState.IDLE);
            return;
        }

        // Request distance reading
        communicationManager.getUltrasonicDistance();

        // Wait for reading and validate
        new Handler(Looper.getMainLooper()).postDelayed(() -> {
            // Validate distance reading
            if (isDistanceValid(currentDistance)) {
                if (currentDistance <= handshakeDistanceThreshold) {
                    System.out.println(TAG + ": Distance validated (" + currentDistance + "cm) - starting greeting");
                    startEnhancedGreeting(trackingId, personId);
                } else {
                    System.out.println(TAG + ": Person too far (" + currentDistance + "cm) - returning to DETECTING");
                    transitionToState(GreetingState.DETECTING);
                }
            } else {
                System.out.println(TAG + ": Invalid distance reading, retrying... (attempt " + (attempt + 1) + ")");
                validateDistanceAndGreet(trackingId, personId, attempt + 1);
            }
        }, 200); // Shorter delay for responsiveness
    }

    /**
     * ENHANCED MULTI-SENSOR VALIDATION - Validates greeting conditions using multiple sensors
     */
    private boolean validateGreetingConditions(Integer trackingId, String personId) {
        // Primary: Ultrasonic + Face Detection
        boolean ultrasonicValid = (currentDistance <= handshakeDistanceThreshold && currentDistance > 2.0f);
        boolean faceValid = (activeFaces.containsKey(trackingId));

        // Secondary: Camera-based distance estimation
        boolean cameraDistanceValid = estimateDistanceFromFaceSize(trackingId);

        // Tertiary: Motion detection (person approaching)
        boolean motionValid = detectApproachingMotion(trackingId);

        // Require at least 2 out of 3 validations
        int validCount = (ultrasonicValid ? 1 : 0) +
                        (cameraDistanceValid ? 1 : 0) +
                        (motionValid ? 1 : 0);

        System.out.println(TAG + ": Multi-sensor validation for " + personId +
            " - Ultrasonic: " + ultrasonicValid +
            ", Camera: " + cameraDistanceValid +
            ", Motion: " + motionValid +
            " (Score: " + validCount + "/3)");

        return validCount >= 2 && faceValid;
    }

    /**
     * Estimate distance from face size (camera-based)
     */
    private boolean estimateDistanceFromFaceSize(Integer trackingId) {
        try {
            // Get face from active faces (this would need to be enhanced to store Face objects)
            // For now, use a simplified approach based on tracking history
            Float lastEstimatedDistance = faceDistanceHistory.get(trackingId);

            if (lastEstimatedDistance != null) {
                // Check if camera-estimated distance is within greeting range
                boolean withinRange = lastEstimatedDistance <= (handshakeDistanceThreshold + 10.0f); // 10cm tolerance
                System.out.println(TAG + ": Camera distance estimate: " + lastEstimatedDistance + "cm, within range: " + withinRange);
                return withinRange;
            }

            return false; // No camera distance data available

        } catch (Exception e) {
            System.out.println(TAG + ": Error in camera distance estimation: " + e.getMessage());
            return false;
        }
    }

    /**
     * Detect approaching motion based on face movement
     */
    private boolean detectApproachingMotion(Integer trackingId) {
        try {
            Long lastMovementTime = faceMovementHistory.get(trackingId);
            long currentTime = System.currentTimeMillis();

            if (lastMovementTime != null) {
                // Check if there's recent movement indicating approach
                long timeSinceMovement = currentTime - lastMovementTime;
                boolean recentMovement = timeSinceMovement < MOTION_DETECTION_WINDOW;

                System.out.println(TAG + ": Motion detection - Time since movement: " + timeSinceMovement + "ms, recent: " + recentMovement);
                return recentMovement;
            }

            return true; // No movement data, assume valid (neutral)

        } catch (Exception e) {
            System.out.println(TAG + ": Error in motion detection: " + e.getMessage());
            return true; // Default to valid on error
        }
    }

    /**
     * Update face tracking data for multi-sensor fusion
     */
    private void updateFaceTrackingData(Integer trackingId, com.google.mlkit.vision.face.Face face) {
        try {
            // Update camera-based distance estimation
            android.graphics.Rect bounds = face.getBoundingBox();
            if (bounds != null) {
                float faceArea = bounds.width() * bounds.height();
                if (faceArea > MIN_FACE_SIZE_FOR_DISTANCE) {
                    // Estimate distance based on face size (inverse relationship)
                    float estimatedDistance = FACE_SIZE_DISTANCE_CALIBRATION / faceArea;
                    faceDistanceHistory.put(trackingId, estimatedDistance);
                }

                // Update motion tracking
                PointF currentCenter = new PointF(bounds.centerX(), bounds.centerY());
                PointF lastPosition = lastFacePositions.get(trackingId);

                if (lastPosition != null) {
                    // Calculate movement
                    float movement = (float) Math.sqrt(
                        Math.pow(currentCenter.x - lastPosition.x, 2) +
                        Math.pow(currentCenter.y - lastPosition.y, 2)
                    );

                    // Update movement history if significant movement detected
                    if (movement > APPROACHING_MOTION_THRESHOLD) {
                        faceMovementHistory.put(trackingId, System.currentTimeMillis());
                        faceMovementSpeeds.put(trackingId, movement);
                    }
                }

                lastFacePositions.put(trackingId, currentCenter);
            }

        } catch (Exception e) {
            System.out.println(TAG + ": Error updating face tracking data: " + e.getMessage());
        }
    }

    /**
     * Validate if distance reading is reliable (enhanced)
     */
    private boolean isDistanceValid(float distance) {
        // Check if distance is within reasonable range
        if (distance < 2.0f || distance > 400.0f) {
            return false;
        }

        // Check for sudden large changes (sensor noise)
        if (previousDistance != 999.0f) {
            float change = Math.abs(distance - previousDistance);
            if (change > 50.0f) { // More than 50cm change is suspicious
                System.out.println(TAG + ": Large distance change detected: " + previousDistance + " -> " + distance);
                return false;
            }
        }

        previousDistance = distance;
        consecutiveValidDistanceReadings++;
        return true;
    }
    
    /**
     * Start enhanced greeting with robust person tracking - FIXED COOLDOWN
     */
    private void startEnhancedGreeting(Integer trackingId, String personId) {
        synchronized (stateLock) {
            if (greetingInProgress) {
                System.out.println(TAG + ": Greeting already in progress - ignoring");
                return;
            }

            long currentTime = System.currentTimeMillis();

            // Mark greeting as in progress
            greetingInProgress = true;
            greetingStartTime = currentTime;

            // Transition to GREETING state
            transitionToState(GreetingState.GREETING);

            // ENHANCED: Update person memory with greeting
            PersonMemoryEntry memoryEntry = personMemoryCache.get(personId);
            if (memoryEntry != null) {
                memoryEntry.updateGreeting();
                System.out.println(TAG + ": Updated greeting in person memory for " +
                    (memoryEntry.recognizedName != null ? memoryEntry.recognizedName : personId));
            }

            // FIXED: Mark person as greeted (legacy support)
            personGreetingHistory.put(personId, currentTime);

            // Clean up old history
            cleanupPersonMemory(currentTime);

            System.out.println(TAG + ": Starting enhanced greeting for person " + personId +
                " (face " + trackingId + ") at distance " + currentDistance + "cm");

            // Send handshake command to ESP32 with retry logic
            sendHandshakeCommandWithRetry(0);

            // Notify callback
            if (callback != null) {
                callback.onGreetingPerformed("Enhanced Handshake + Voice");
            }

            // Schedule greeting end after configured delay
            new Handler(Looper.getMainLooper()).postDelayed(() -> {
                endGreeting();
            }, GREETING_RETURN_DELAY);
        }
    }

    /**
     * Send handshake command with retry logic
     */
    private void sendHandshakeCommandWithRetry(int attempt) {
        if (attempt >= MAX_RETRY_ATTEMPTS) {
            System.out.println(TAG + ": Max handshake command attempts reached");
            return;
        }

        if (communicationManager != null) {
            try {
                // Send handshake command to ESP32 (this includes TTS greeting)
                communicationManager.sendHandshakeGreeting();
                System.out.println(TAG + ": Handshake command sent to ESP32 (attempt " + (attempt + 1) + ")");
            } catch (Exception e) {
                System.out.println(TAG + ": Handshake command failed, retrying... " + e.getMessage());
                new Handler(Looper.getMainLooper()).postDelayed(() -> {
                    sendHandshakeCommandWithRetry(attempt + 1);
                }, 500); // 500ms retry delay
            }
        } else {
            System.out.println(TAG + ": Communication manager not available for handshake");
        }
    }

    /**
     * End greeting and return to cooldown state
     */
    private void endGreeting() {
        synchronized (stateLock) {
            if (!greetingInProgress) {
                return;
            }

            System.out.println(TAG + ": Ending greeting - returning arm to rest position");

            // Send greeting end command to ESP32
            if (communicationManager != null) {
                try {
                    communicationManager.sendSensorCommand("GREETING_END", null);
                    System.out.println(TAG + ": Greeting end command sent to ESP32");
                } catch (Exception e) {
                    System.out.println(TAG + ": Error sending greeting end command: " + e.getMessage());
                }
            }

            // Mark greeting as completed
            greetingInProgress = false;
            greetingStartTime = 0;

            // Transition to cooldown state
            transitionToState(GreetingState.COOLDOWN);

            // Schedule return to idle after cooldown
            new Handler(Looper.getMainLooper()).postDelayed(() -> {
                if (currentState == GreetingState.COOLDOWN) {
                    transitionToState(GreetingState.IDLE);
                    System.out.println(TAG + ": Cooldown completed, ready for new greetings");
                }
            }, 2000); // 2 second cooldown before accepting new greetings

            System.out.println(TAG + ": Enhanced greeting completed, entering cooldown");
        }
    }
    
    /**
     * Clean up old tracked faces that haven't been seen recently
     */
    private void cleanupOldTrackedFaces(long currentTime) {
        // Remove faces that haven't been seen for a while
        activeFaces.entrySet().removeIf(entry ->
            (currentTime - entry.getValue()) > FACE_TRACKING_TIMEOUT);

        // Note: faceGreetingStatus removed in favor of person-based memory system

        // Clean up face to person mapping for inactive faces
        faceToPersonMapping.entrySet().removeIf(entry ->
            !activeFaces.containsKey(entry.getKey()));

        // Clean up face quality scores for inactive faces
        faceQualityScores.entrySet().removeIf(entry ->
            !activeFaces.containsKey(entry.getKey()));
    }
    
    // ESP32CommunicationManager.CommunicationListener implementation
    @Override
    public void onCommandSent(String controllerId, RobotCommand command) {
        // Not needed for this implementation
    }

    @Override
    public void onResponseReceived(String controllerId, RobotResponse response) {
        // Parse ultrasonic distance data from sensor responses
        if ("sensor".equals(controllerId) && response.getData() != null) {
            Object distanceObj = response.getData().get("ultrasonic_distance");
            if (distanceObj instanceof Number) {
                currentDistance = ((Number) distanceObj).floatValue();
                System.out.println(TAG + ": Distance updated: " + currentDistance + "cm");

                // Check if we should trigger greeting based on distance
                checkDistanceForGreeting();
            }
        }
    }

    /**
     * Check if current distance warrants a greeting for any active faces - UPDATED FOR STATE MACHINE
     */
    private void checkDistanceForGreeting() {
        if (!isSmartGreetingEnabled || !isUltrasonicSensorEnabled || currentState != GreetingState.IDLE) {
            System.out.println(TAG + ": Distance greeting check skipped - SmartGreeting: " + isSmartGreetingEnabled +
                              ", Sensor: " + isUltrasonicSensorEnabled + ", State: " + currentState);
            return;
        }

        long currentTime = System.currentTimeMillis();
        System.out.println(TAG + ": Checking distance-based greeting - Active faces: " + activeFaces.size() +
                          ", Distance: " + currentDistance + "cm, Threshold: " + handshakeDistanceThreshold + "cm");

        // Check all active faces for potential greetings
        for (Integer trackingId : activeFaces.keySet()) {
            String personId = faceToPersonMapping.get(trackingId);
            if (personId != null && shouldGreetPerson(personId, currentTime) &&
                currentDistance <= handshakeDistanceThreshold &&
                currentDistance > 0) {

                // Start the state machine process
                currentTrackedFaceId = trackingId;
                currentPersonId = personId;
                transitionToState(GreetingState.VALIDATING);
                checkDistanceAndGreet(trackingId, personId);

                System.out.println(TAG + ": Distance-triggered greeting for person " + personId +
                                  " (face " + trackingId + ") at " + currentDistance + "cm");
                break; // Only greet one person at a time
            } else {
                System.out.println(TAG + ": Greeting conditions not met for person " + personId +
                                  " - ShouldGreet: " + (personId != null ? shouldGreetPerson(personId, currentTime) : "null") +
                                  ", Distance: " + currentDistance + " <= " + handshakeDistanceThreshold +
                                  ", Valid: " + (currentDistance > 0));
            }
        }
    }

    @Override
    public void onConnectionStatusChanged(String controllerId, boolean isConnected) {
        System.out.println(TAG + ": Connection status changed: " + isConnected);
    }

    @Override
    public void onError(String controllerId, String error) {
        System.err.println(TAG + ": Communication error: " + error);
    }

    // Configuration methods for settings
    public void setGreetingDistance(float distance) {
        if (distance > 0 && distance <= 200) {
            HANDSHAKE_DISTANCE_THRESHOLD = distance;
            System.out.println(TAG + ": Greeting distance set to " + distance + "cm");
        }
    }

    public void setFaceDetectionDuration(long duration) {
        if (duration >= 500 && duration <= 10000) {
            FACE_DETECTION_DURATION = duration;
            System.out.println(TAG + ": Face detection duration set to " + duration + "ms");
        }
    }

    public void setGreetingReturnDelay(long delay) {
        if (delay >= 1000 && delay <= 30000) {
            GREETING_RETURN_DELAY = delay;
            System.out.println(TAG + ": Greeting return delay set to " + delay + "ms");
        }
    }

    public void setGreetingCooldown(long cooldown) {
        if (cooldown >= 5000 && cooldown <= 60000) {
            GREETING_COOLDOWN = cooldown;
            System.out.println(TAG + ": Greeting cooldown set to " + cooldown + "ms");
        }
    }

    // Getter methods for current settings
    public float getGreetingDistance() { return HANDSHAKE_DISTANCE_THRESHOLD; }
    public long getFaceDetectionDuration() { return FACE_DETECTION_DURATION; }
    public long getGreetingReturnDelay() { return GREETING_RETURN_DELAY; }
    public long getGreetingCooldown() { return GREETING_COOLDOWN; }

    /**
     * Get current status
     */
    public boolean isSmartGreetingEnabled() {
        return isSmartGreetingEnabled;
    }
    
    public float getCurrentDistance() {
        return currentDistance;
    }
    
    public float getHandshakeDistanceThreshold() {
        return handshakeDistanceThreshold;
    }
    
    public void setHandshakeDistanceThreshold(float threshold) {
        this.handshakeDistanceThreshold = threshold;
    }

    /**
     * Test smart greeting functionality - DEBUG METHOD
     */
    public void testSmartGreeting() {
        System.out.println(TAG + ": ========== TESTING SMART GREETING ==========");
        System.out.println(TAG + ": Current Settings:");
        System.out.println(TAG + ": - Smart Greeting Enabled: " + isSmartGreetingEnabled);
        System.out.println(TAG + ": - Ultrasonic Sensor Enabled: " + isUltrasonicSensorEnabled);
        System.out.println(TAG + ": - Distance Threshold: " + handshakeDistanceThreshold + "cm");
        System.out.println(TAG + ": - Current Distance: " + currentDistance + "cm");
        System.out.println(TAG + ": - Active Faces: " + activeFaces.size());
        System.out.println(TAG + ": - Current State: " + currentState);

        // Simulate face detection
        System.out.println(TAG + ": Simulating face detection...");
        onFaceCountUpdated(1);

        // Simulate distance within threshold
        System.out.println(TAG + ": Simulating distance within threshold (25cm)...");
        updateDistance(25.0f);

        System.out.println(TAG + ": Test completed. Check logs for greeting trigger.");
        System.out.println(TAG + ": ==========================================");
    }

    /**
     * Get current smart greeting status for debugging
     */
    public String getDebugStatus() {
        StringBuilder status = new StringBuilder();
        status.append("SmartGreeting Status:\n");
        status.append("- Enabled: ").append(isSmartGreetingEnabled).append("\n");
        status.append("- Sensor: ").append(isUltrasonicSensorEnabled).append("\n");
        status.append("- Distance: ").append(currentDistance).append("cm (threshold: ").append(handshakeDistanceThreshold).append("cm)\n");
        status.append("- Faces: ").append(activeFaces.size()).append("\n");
        status.append("- State: ").append(currentState).append("\n");
        status.append("- Communication: ").append(communicationManager != null ? "Connected" : "Disconnected");
        return status.toString();
    }

    /**
     * Cleanup resources - UPDATED FOR NEW ARCHITECTURE
     */
    public void cleanup() {
        synchronized (stateLock) {
            if (communicationManager != null) {
                communicationManager.setCommunicationListener(null);
            }

            // Clean up all tracking data
            activeFaces.clear();
            personGreetingHistory.clear();
            faceToPersonMapping.clear();
            faceFirstDetectionTime.clear();
            faceQualityScores.clear();

            // Clean up person memory system
            personMemoryCache.clear();
            faceEmbeddingCache.clear();
            faceDistanceHistory.clear();
            faceMovementHistory.clear();
            lastFacePositions.clear();
            faceMovementSpeeds.clear();

            // Reset state
            currentState = GreetingState.IDLE;
            currentTrackedFaceId = null;
            currentPersonId = null;
            greetingInProgress = false;

            System.out.println(TAG + ": SmartGreetingManager cleaned up");
        }
    }
}
